<template>
  <div class="screen-page" :style="{ height: `calc(100vh - ${titleHeight}px)`, paddingTop: `${titleHeight}px`}">
    <div class="screen-container">
      <div class="header-container">
        <van-search ref="searchRef" v-model="searchText" placeholder="搜索求鉴关键词…" shape="round">
          <template #left-icon>
            <img class="search-icon" :src="searchIcon">
          </template>
        </van-search>
      </div>
      <div v-if="historyList.length" class="history-container">
        <div class="title-container history-title">
          <div class="history-left">
            <img class="image-icon" :src="historyIcon"/>
            <div class="title-text">搜索历史</div>
          </div>
          <div class="history-right">
            <img v-show="!historyDel" class="image-icon" :src="delIcon" @click="handleChange"/>
            <div class="pt-5">
              <span v-show="historyDel" class="mr-40" @click="handleAllDel">全部删除</span>
              <span v-show="historyDel" @click="handleChange">完成</span>
            </div>
          </div>
        </div>
        <div class="list-container">
          <div class="tags-container" v-for="(item, index) in historyList" :key="index">
            <div class="tag-item" @click="handleSearch(item)">
              <div>{{ item }}</div>
              <img v-show="historyDel" class="tag-close" :src="closeIcon" @click="handleDelHistory(item)"/>
            </div>
          </div>
        </div>
      </div>
      <div class="classify" :class="{'mt-5': !historyList.length}">
        <TitleTop title="所属品类" :right="false"/>
      </div>
      <div class="tag-container" v-if="isDataLoaded">
        <div
            v-for="(item, index) in categoryList"
            :key="index"
            class="tag-item"
            :class="{'active-item': isActive(1,'category', item.id)}"
            @click="toggleSelection('category', item)"
        >
          <div>{{ item.name }}</div>
          <div v-if="isActive(1,'category', item.id)" class="check-icon">
            <img class="check-img" :src="answerIcon"/>
          </div>
        </div>
      </div>
      <div class="classify mt-10">
        <TitleTop title="求鉴状态" :right="false"/>
      </div>
      <div class="tag-container" v-if="isDataLoaded">
        <div
            v-for="(item, index) in filteredTags"
            :key="index"
            class="tag-item"
            :class="{'active-item': isActive( 2,'state', item.id)}"
            @click="toggleSelection('state', item)"
        >
          <div>{{ item.name }}</div>
          <div v-if="isActive( 2,'state', item.id)" class="check-icon">
            <img class="check-img" :src="answerIcon"/>
          </div>
        </div>
      </div>
      <div class="classify mt-10">
        <TitleTop title="所属品牌" :right="false"/>
      </div>
      <div class="tag-container" v-if="isDataLoaded">
        <div
            v-for="(item, index) in brandList"
            :key="index"
            class="tag-item"
            :class="{'active-item': isActive(1,'brand', item.id)}"
            @click="toggleSelection('brand', item)"
        >
          <div>{{ item.name }}</div>
          <div v-if="isActive(1,'brand', item.id)" class="check-icon">
            <img class="check-img" :src="answerIcon"/>
          </div>
        </div>
      </div>
      <div class="footer-box">
        <div class="btn left-btn" @click="handleCancel">取消</div>
        <div class="btn right-btn" @click="handleSave">应用</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, computed, nextTick} from "vue";
import {getCache, getCookie, setCache} from "@/utils/cache.js";
import TitleTop from "@/components/titleTop.vue";
import searchIcon from "@/assets/images/identify/searchIcon.png";
import historyIcon from '@/assets/images/common/historyIcon.png'
import delIcon from '@/assets/images/common/delIcon.png'
import closeIcon from '@/assets/images/common/closeIcon.png'
import answerIcon from "@/assets/images/common/answerIcon.png"
import shareIcon from "@/assets/images/common/shareIcon.png"
import router from "@/router/index.js";
import {getQueryParam, otherLogin} from "@/utils/common.js";
import {commonCategory} from "@/views/identify/lists.js";
import {useAuthStore} from "@/stores/auth.js";
import {useRoute} from "vue-router";


const route = useRoute()
const authStore = useAuthStore();


const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const searchRef = ref();
const searchText = ref('');
const backFlag = ref(false);
const userMes = ref();
const historyDel = ref(false)
const historyList = ref([])
// 标记数据是否已加载完成
const isDataLoaded = ref(false);
// const tags = ref({
//   category: ['全部'],
//   state: ['全部'],
//   brand: ['全部']
// });

const tags = ref({
  category: -1,
  state: 0,
  brand: -1
});

const categoryList = ref([]);

const stateList = ref([
  {name: '全部', id: 0},
  {name: '待鉴定', id: 1},
  {name: '已鉴定', id: 2},
  {name: '推荐', id: 4},
  {name: '争议', id: 3},
]);


const filteredTags = computed(() => {
  if (userMes.value?.train) {
    return stateList.value.slice(0, 3);
  } else {
    return stateList.value;
  }
})

const brandList = ref([]);

function isActive(state, type, id) {
  return tags.value[type] === id;
  // if(state === 1) {
  //   return tags.value[type] === name;
  // } else {
  //   // return tags.value[type].includes(name);
  //   return name.includes(tags.value[type]);
  // }
}

function toggleSelection(type, item) {
  tags.value[type] = item.id;
  //下面是多选的逻辑 请勿删除
  // const currentTags = tags.value[type];
  // let prevCount = currentTags.length;
  //
  // if (item.name === '全部') {
  //   tags.value[type] = ['全部'];
  // } else {
  //   if (currentTags.includes('全部')) {
  //     tags.value[type] = [item.name];
  //   } else {
  //     if (currentTags.includes(item.name)) {
  //       tags.value[type] = currentTags.filter(name => name !== item.name);
  //     } else {
  //       tags.value[type].push(item.name);
  //     }
  //   }
  //   if (tags.value[type].length === 0) {
  //     tags.value[type] = ['全部'];
  //   }
  // }
}

// const totalSelectedTags = computed(() => {
//   let count = 0;
//   for (const type in tags.value) {
//     count += tags.value[type].length;
//     if (tags.value[type].includes('全部')) {
//       count -= (tags.value[type].length - 1);
//     }
//   }
//   return count;
// });

const handleChange = () => {
  historyDel.value = !historyDel.value
}

const handleAllDel = () => {
  setCache('HISTORY_LIST', JSON.stringify([]));
  historyList.value = []
}

const handleDelHistory = (val) => {
  let list = historyList.value
  list = list?.filter(item => item !== val);
  setCache('HISTORY_LIST', JSON.stringify(list));
  if (getCache('HISTORY_LIST')) {
    historyList.value = JSON.parse(getCache('HISTORY_LIST'))
  }
}


const handleSearch = (item) => {
  if (historyDel.value) return
  searchText.value = item
  handleUpdate()
}


const handleCancel = () => {
  if (authStore.phone) {
    window.mag.closeWin();
  } else {
    router.back()
  }
}


const handleUpdate = () => {
  if (!searchText.value) return
  historyList.value = historyList?.value?.filter(item => item !== searchText.value);
  historyList.value.unshift(searchText.value)
  historyList.value = historyList.value.length > 10 ? historyList.value.splice(0, 10) : historyList.value
  setCache('HISTORY_LIST', JSON.stringify(historyList.value));
}


const handleSave = async () => {
  await handleUpdate()
  const {category, state, brand} = tags.value
  setCache("SEARCH_TEXT", searchText.value)
  if (authStore.phone) {
    // if (backFlag.value === 'true') {
    //   window.mag.closeWin();
    // } else {

    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/brand/index?mag_hide_progress=1`, {
      // searchText: searchText.value,
      brandKey: brand,
      stateKey: state,
      categoryKey: category,
      btnType: 1,
    });
    // }
  } else {
    // if (backFlag.value === 'true') {
    //   router.back()
    // } else {
    router.push({
      name: 'brand',
      query: {
        // searchText: searchText.value,
        brandKey: brand,
        stateKey: state,
        categoryKey: category,
        btnType: 1,
      }
    })
    // }
  }

}

const showList = () => {
  // let cateList = JSON.parse(getCache('CATEGORY_LIST'))
  // categoryList.value = cateList
  categoryList.value = commonCategory.value
  let brand = JSON.parse(getCache('BRAND_LIST'))
  brandList.value = brand
}

onMounted(() => {
  showList()
  userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
  if (authStore.phone) {
    window.mag.setTitle('筛选');
  } else {
    document.title = '筛选'
  }
  // if (!authStore.token) {
  //   otherLogin()
  // }
  if(authStore.phone) {
    if(getCookie('cbo_magapp_token')?.length > 1) {
      otherLogin()
    }
  }
  window.mag.showNavigation()
  window.mag.setData({
    shareData: {
      title: '中羽在线',
      des: '',
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: window.location.href,
    }
  });
  // window.mag.addRefreshComponent()
  backFlag.value = route.query.backFlag || getQueryParam('backFlag')
  if (getCache('HISTORY_LIST')) {
    historyList.value = JSON.parse(getCache('HISTORY_LIST'))
  }
  searchText.value = route.query.searchText || getQueryParam('searchText') || '';
  tags.value = {
    brand: Number(route.query.brandKey) || Number(getQueryParam('brandKey')) || -1,
    category: Number(route.query.categoryKey) || Number(getQueryParam('categoryKey')),
    state: Number(route.query.stateKey) || Number(getQueryParam('stateKey'))
  };
  if (Number(route.query.type) === 1 || Number(getQueryParam('type')) === 1) {
    tags.value.brand = -1
    // nextTick(()=>{
    //   // searchRef.value.focus();
    //   // searchText.value = searchText.value + ''
    // });
    // setTimeout(() => {
    //   searchRef.value.focus();
    // },1000)
  }
  // 数据加载完成后设置为 true
  isDataLoaded.value = true;
})

</script>

<style scoped lang="scss">
.screen-page {
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: #FFFFFF;

  .screen-container {
    padding: 12px;
  }

  .header-container {
    .van-search {
      padding: 0;
    }

    .search-icon {
      height: 14px;
      width: 14px;
      margin-top: 6px;
    }
  }

  .history-container {
    margin: 15px 0 0;
  }

  .history-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .image-icon {
      width: 18px;
      height: 18px;
      margin-right: 6px;
    }

    .title-text {
      color: #3D3D3D;
      font-size: 14px;
    }

    .history-left {
      display: flex;
      align-items: center;
    }

    .history-right {
      height: 18px;
      color: #3D3D3D;
      font-size: 13px;
      font-weight: 400;
    }
  }

  .list-container {
    display: flex;
    flex-wrap: wrap;
    padding: 15px 0 3px;

    .tags-container {
      margin: 0 10px 10px 0;

      .tag-item {
        position: relative;
        color: #3D3D3D;
        font-size: 13px;
        font-weight: 400;
        padding: 6px 10px;
        border-radius: 5px;
        background-color: #F5F5F5;
      }

      .tag-close {
        width: 16px;
        height: 16px;
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
      }
    }
  }

  .classify {
    padding: 12px 0;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;

    .tag-item {
      position: relative;
      width: calc((100% - 40px) / 4);
      color: #3D3D3D;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      padding: 10px 0;
      border-radius: 5px;
      background-color: #F5F5F5;
      cursor: pointer;
    }

    .active-item {
      color: #478E87;
      background-color: #DFF7F6;
    }

    .active-item::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 200%;
      height: 200%;
      border-radius: 10px;
      border: 1px solid #93C3BF;
      transform: scale(0.5);
      transform-origin: 0 0;
      box-sizing: border-box;
    }
  }

  .check-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    border-radius: 5px 0 5px 0;
    background-color: #93C3BF;

    .check-img {
      width: 6px;
      height: 6px;
      padding: 3px;
    }
  }

  .footer-box {
    position: absolute;
    bottom: 20px;
    width: 94%;
    display: flex;
    justify-content: space-between;

    .btn {
      width: 50%;
      font-size: 16px;
      text-align: center;
    }

    .left-btn {
      padding: 11px 0;
      color: #478E87;
      border-radius: 22px;
      background-color: #DFF7F6;
      margin-right: 15px;
    }

    .right-btn {
      padding: 11px 0;
      color: #FFFFFF;
      border-radius: 22px;
      background-color: #478E87;
    }
  }
}
</style>