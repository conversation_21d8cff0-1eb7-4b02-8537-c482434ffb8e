import './assets/reset.css'
import './styles/common.scss'
import { createApp } from 'vue'
import { setRem } from "./utils/rem";
import pinia from "@/stores/index.js";
import Vconsole from "vconsole"
import { Lazyload } from "vant";

new Vconsole()

import App from './App.vue'
import router from './router'

const app = createApp(App)
setRem(375, 37.5)
app.use(Lazyload, {
    lazyComponent: true,
});
app.use(pinia)
app.use(router)

// 定义全局变量
app.config.globalProperties.$apiUrl = 'http://192.168.31.146:3000';
app.mount('#app')
