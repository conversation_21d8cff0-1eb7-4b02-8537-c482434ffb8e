<template>
  <div class="item-user">
    <div class="user-left" @click="handleJumpUser(item)">
      <!--      <img v-if="item?.user?.avatar" loading="lazy" class="user-image" :src="item.user.avatar || avatar"/>-->
      <van-image
          v-if="item?.user?.avatar"
          class="user-image"
          lazy-load
          :src="item.user.avatar || avatar"
      />
      <div class="user-mes">
        <div class="user-name">{{ item?.user?.name }}</div>
        <div class="detail-mes mt-3">
          <div v-if="item?.user?.groupId" class="user-tag mr-5"
               :class="[Number(item?.user?.groupId) === 1 ? 'orange' :
                Number(item?.user?.groupId) === 2 ? 'orange' :
                Number(item?.user?.groupId) === 40 ? 'golden' :
                Number(item?.user?.groupId) === 3 ? 'green' :
                Number(item?.user?.gender) === 2? 'pink' : 'purple']">
            {{ userGroupList[item?.user?.groupId].groupName }}
          </div>
          <div class="user-number mr-3">{{ item.createTime }}</div>
          <div class="user-number">{{ `${item.read}阅读` }}</div>
        </div>
      </div>
    </div>
    <div class="flex-vhc">
      <div v-if="userInfo.id === item?.user?.uid" class="red-tip-box">
        <img class="red-tip-icon" :src="redTipIcon"/>
        <span class="red-tip-text">推广</span>
      </div>
      <img v-if="userInfo.id === item?.user?.uid || [1,2].includes(userInfo.groupId) || Number(userInfo.id) === 117229 "
           class="more-icon" :src="moreIcon" @click="handleMore"/>
    </div>
  </div>
</template>

<script setup>
import avatar from "@/assets/images/common/avatar.png"
import equipIcon from "@/assets/images/details/equipIcon.png"
import topIcon from "@/assets/images/details/topIcon.png"
import moreIcon from "@/assets/images/details/moreIcon.png"
import redTipIcon from "@/assets/images/details/redTip.png"
// import commonApi from "@/api/common";
import {ref, computed, watch} from "vue";
import {getCache} from "@/utils/cache";
import {useAuthStore} from "@/stores/auth.js";
import {isMagApp, jumpUser} from "@/utils/common.js";

const authStore = useAuthStore();
import {setCache} from "@/utils/cache";
import {useRouter} from "vue-router";
import {otherList, categoryBrandMap} from '@/views/identify/lists'

const router = useRouter()
const filteredArray = ref(0)


const props = defineProps({
  item: {
    type: Object,
    default: {}
  }
})

const emit = defineEmits(['more'])

const tagMap = {
  1: '金牌球友',
  2: '入门'
}
const handleAvatar = () => {
  console.log('点击头像')
}

// const handleFocus = async (item) => {
//   if (!userInfo.value.id) {
//     jumpLogin()
//     return
//   }
//   let res = await commonApi.addUserFocus({userId: item.userInfo.userId})
//   if (res.code === 200) {
//     item.isFocus = !item.isFocus
//     item.isFocus ? toast('您已关注') : toast('您已取消关注')
//   }
// }

const handleMore = () => {
  emit('more')
}

const handleJumpUser = (item) => {
  // jumpUser(item.user.uid)
  if (isMagApp()) {
    window.mag.newWin(`magapp://userHome?userId=${item.user.uid}`)
  } else {
    alert('请下载中羽在线APP使用')
  }
  // if(ment !== 'magApp'){
  //  alert('请下载中羽在线APP使用')
  // } else if(ment === 'magApp') {
  //   window.mag.newWin(`magapp://userHome?userId=${item.user.uid}`)
  // } else {
  //  let jumpUrl = `https://{appHost}/magshare/{siteId}?jump_url={jump_url}&content_url={content_url}`
  //   const returnUrl = encodeURIComponent(window.location.href);
  //   let jumpUrl = `https://add.badmintoncn.com/magshare/cboapp?jump_url=${returnUrl}`
  //   console.log('jumpUrl', jumpUrl)
  //   window.location.href = jumpUrl;
  // }
}

const handleImprove = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/brand/improve?mag_hide_progress=1`, {id: props.item.id});
  } else {
    router.push({
      name: 'improve',
      query: {
        id: props.item.id,
      }
    })
  }
}

const mergeArrays = (partial, all) => {
  const result = [];
  partial?.forEach((item, index) => {
    if (all[index] && partial[index].path && !partial[index].extra) {
      result.push({
        name: all[index].name,
        icon: all[index].icon,
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        createTime: partial[index].createTime
      });
    } else if (partial[index].path) {
      result.push({
        name: '其他补充',
        icon: '',
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        createTime: partial[index].createTime
      });
    }
  });
  return result;
};

watch(() => props.item,
    (val) => {
      let list = categoryBrandMap[val?.cateName]?.[val?.brandName] ?? categoryBrandMap[val?.cateName] ?? otherList.value;
      const newList = mergeArrays(val.attachList, list);
      filteredArray.value = newList.filter(item => item.name === "其他补充");
    }, {
      deep: true,
      immediate: true
    })


// 用户信息
const userInfo = computed(() => {
  return authStore.userInfo
})

const userGroupList = computed(() => {
  return JSON.parse(getCache('USER_GROUP_LIST')).reduce((obj, item) => {
    obj[item.groupId] = item
    return obj
  }, {})
})

</script>

<style lang="scss" scoped>
.item-user {
  margin: 5px 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-left {
  display: flex;
  align-items: center;
  align-content: center;

  .user-image {
    width: 40px;
    height: 40px;
    overflow: hidden;
    border-radius: 50%;
  }

  .user-mes {
    padding-left: 8px;

    .user-name {
      color: #3D3D3D;
      font-size: 14px;
      font-weight: 600;
    }

    .detail-mes {
      display: flex;
      align-items: center;
      padding-top: 3px;

      .user-number {
        color: #999999;
        font-size: 10px;
      }
    }
  }

  .user-tag {
    display: flex;
    align-items: center;
    font-size: 10px;
    padding: 2px 3px 1px;
    color: #FFFFFF;
    border-radius: 3px;
  }

  .pink {
    background-color: #FF7CAA;
  }

  .purple {
    background-color: #819FFF;
  }

  .orange {
    background-color: #FF8F1F;
  }

  .golden {
    color: #3D3D3D;
    font-weight: 500;
    border: 1px solid #FFE3B9;
    background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
  }

  .green {
    background-color: #00AC72;
  }
}

.equip-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.item-btn {
  color: #999999;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 20px;
  border: 1px solid #D8D8D8;
}

.item-add-btn {
  display: flex;
  color: #FFFFFF;
  background-color: #478E87;

  .btn-icon {
    margin-right: 3px;
  }
}

.improve-btn {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 20px;
  background-color: #FA5151;

  .icon-btn {
    width: 10px;
    height: 10px;
    margin-right: 3px;
  }
}

.red-tip-box {
  position: relative;

  .red-tip-icon {
    width: 33px;
    height: 20px;
    margin-bottom: 3px;
  }

  .red-tip-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-54%, -40%);
    white-space: nowrap;
    color: #FFFFFF;
    font-size: 10px;
    font-weight: 600;
  }
}

.more-icon {
  width: 20px;
  height: 4px;
  //margin-left: 12px;
  margin-left: 5px;
}
</style>