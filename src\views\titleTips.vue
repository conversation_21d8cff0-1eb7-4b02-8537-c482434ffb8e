<template>
  <div class="title-box" id="myTitle">
    <div class="left-box" @click="handleJump">
      <img class="theme-icon" :src="themeIcon"/>
      <span>在【中羽APP】查看</span>
    </div>
    <div class="right-box" @click="jumpAppLink">
      <span>下载APP</span>
    </div>
  </div>
</template>

<script setup>
import themeIcon from "@/assets/images/common/themeIcon.png"

var browser = {
  versions: function () {
    var u = navigator.userAgent, app = navigator.appVersion;
    return {
      trident: u.indexOf('Trident') > -1, // IE内核
      presto: u.indexOf('Presto') > -1, // Opera内核
      webKit: u.indexOf('AppleWebKit') > -1, // 苹果、谷歌内核
      gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, // 火狐内核
      mobile: !!u.match(/AppleWebKit.*Mobile.*/), // 是否为移动终端
      ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // iOS终端
      android: u.indexOf('Android') > -1 || u.indexOf('Adr') > -1, // 安卓终端
      iPhone: u.indexOf('iPhone') > -1, // 是否为iPhone或者QQHD浏览器
      iPad: u.indexOf('iPad') > -1, // 是否iPad
      webApp: u.indexOf('Safari') == -1, // 是否Web应该程序，没有头部与底部
      weixin: u.indexOf('MicroMessenger') > -1, // 是否微信
      qq: u.match(/\sQQ/i) == " qq" // 是否QQ
    };
  }()
};

const copyUrl = (id) => {
  if (browser.versions.android) {
    const element = document.getElementById(id); // 获取元素
    element.style.display = "block"; // 显示元素
    element.select(); // 选择文本
    document.execCommand("Copy"); // 执行复制命令
    element.style.display = "none"; // 隐藏元素
  }
}

const copyText = (text) => {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.style.position = "absolute";
  textarea.style.opacity = "0";
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand("copy");
  document.body.removeChild(textarea);
};


const isAndroid = () => {
  let userAgent = navigator.userAgent.toLowerCase();
  return userAgent.indexOf("android") > -1;
}


const handleJump = () => {
  const curUrl = encodeURIComponent(window.location.href);
  let jumpUrl = `https://app.badmintoncn.com/magshare/cboapp?jump_url=${curUrl}`
  if (isAndroid()) {
    copyText(jumpUrl)
  }
  document.location = jumpUrl
}

const jumpAppLink = () => {
  if (browser.versions.ios || browser.versions.iPhone || browser.versions.iPad) {
    // 如果是iOS设备，跳转到App Store中的应用页面
    document.location = "https://itunes.apple.com/cn/app/zhong-yu-zai-xian/id1033070952?mt=8";
  } else if (browser.versions.android) {
    // 如果是安卓设备，跳转到应用宝中的应用页面
    document.location = "https://a.app.qq.com/o/simple.jsp?pkgname=com.badmintoncn.bbs";
  } else {
    // 如果设备类型不是iOS或安卓，也跳转到应用宝中的应用页面
    document.location = "https://a.app.qq.com/o/simple.jsp?pkgname=com.badmintoncn.bbs";
  }
}
</script>

<style lang="scss" scoped>
.title-box {
  //position: sticky;
  //top: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  border-bottom: 1px #eee solid;
  background: #F7F7F7;
  z-index: 999;

  .left-box {
    display: flex;
    align-items: center;
    font-size: 14px;

    .theme-icon {
      width: 35px;
      height: 35px;
      margin-right: 10px;
    }
  }

  .right-box {
    color: #FFFFFF;
    font-size: 14px;
    border-radius: 50px;
    text-align: center;
    padding: 6px 12px;
    background: #111111;
  }
}
</style>