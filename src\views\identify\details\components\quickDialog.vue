<template>
  <van-overlay :show="visible" :opacity="0.3" z-index="999">
    <div class="common-dialog" v-if="visible">
      <div class="dialog">
        <div class="title-box">
          <div style="width: 20px"></div>
          <div class="title" v-if="title">{{ title }}</div>
          <img @click="hide" class="close" :src="closeIcon"/>
        </div>
        <div class="content" v-if="$slots.default">
          <slot></slot>
        </div>
        <div class="content" v-html="content" v-else/>
        <div v-if="footer" class="btn-group">
          <div class="btn btn-left" @click="onBtnLeftClick">{{ btnLeftTxt }}</div>
          <div class="btn btn-right" @click="onBtnRightClick">{{ btnRightTxt }}</div>
        </div>
      </div>
      <div class="common-mask"/>
    </div>
  </van-overlay>
</template>

<script setup>
import {ref} from "vue";
import closeIcon from "@/assets/images/common/closeIcon.png"

const props = defineProps({
  title: {
    required: true,
    type: String,
  },
  content: {
    type: String,
  },
  footer: {
    type: Boolean,
    default: true,
  },
  btnLeftTxt: {
    type: String,
    default: () => "取 消",
  },
  btnRightTxt: {
    type: String,
    default: () => "确 认",
  },
  zIndex: {
    type: [Number, String],
  },
})

const visible = ref(false)

const show = () => {
  visible.value = true
}

const hide = () => {
  visible.value = false
}


const emit = defineEmits(['leftClick', 'rightClick'])
const onBtnLeftClick = () => {
  hide()
  emit('leftClick')
}

const onBtnRightClick = () => {
  emit('rightClick')
  // hide()
}

defineExpose({show, hide}) // 提供 openModal 方法
</script>

<style scoped lang="scss">
.common-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 3999;
}

.dialog {
  width: 305px;
  padding: 20px 16px;
  border-radius: 15px;
  background-color: #FFFFFF;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.title-box {
  display: flex;
  justify-content: space-between;
}

.title {
  line-height: 24px;
  margin-bottom: 18px;
  color: #3D3D3D;
  font-size: 16px;
  text-align: center;
  font-weight: bold;
}

.close {
  width: 20px;
  height: 20px;
}

.content {
  //line-height: 42rpx;
  line-height: 1.5;
  margin-bottom: 20px;
  padding: 0 9px;
  color: #3D3D3D;
  font-size: 14px;
  text-align: center;
}

.btn-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.btn {
  font-size: 16px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  width: 145px;
  border-radius: 22px;
}

.btn-left {
  color: #478E87;
  background-color: #DFF7F6;
}

.btn-right {
  color: #FFFFFF;
  background-color: #478E87;
}
</style>
