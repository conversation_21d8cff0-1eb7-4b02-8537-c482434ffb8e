<template>
  <div class="page-container">
    <div class="container" @click="handlePictures">点击测试拍照</div>
  </div>
</template>

<script setup>
import identifyApi from "@/services/identify.js";

const handlePictures = async () => {
  let res = await identifyApi.getSearch({})
  console.log('res', res)
}
</script>

<style scoped lang="scss">
.page-container {
  width: 100vw;
  height: 100vh;
  background-color: gray;
  overflow: hidden;
  .container {
    margin-top: 300px;
    width: 750px;
    height: 100px;
    font-size: 24px;
    background-color: pink;
  }
}

</style>
