<template>
  <van-popup
      v-model:show="precautionsShow"
      round
      position="bottom"
  >
    <div class="precautions-popup">
      <div class="title-box">
        <div></div>
        <div>鉴定训练事项</div>
        <img class="close-icon" :src="closeIcon" @click="hide"/>
      </div>
      <div class="tips mt-8 flex-col-hc pb-25">温馨提示：本训练用于想认真学习装备鉴定的爱好者</div>
      <div class="content-box">
        <div v-for="(item, index) in list" :key="index">
          <div class="content-text">{{item.title}}</div>
        </div>
      </div>
      <div class="footer-btn" @click="changeKnow">
        我知道了
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref } from "vue";
import closeIcon from '@/assets/images/common/closeIcon.png'



const precautionsShow = ref(false)
const emit = defineEmits(['close'])

const list = ref([
  {title: '1、开启训练模式，表态不受用户组等级限制，但所有鉴定数据不参与正式统计，训练中所有正式表态数据将不可见，表态后才可见'},
  {title: '2、训练表态与正式表态为互斥，同一帖已在训练中表态过在正式中不可表态，反之同理'},
  {title: '3、用户组低于“业余选手”的训练可获得一定的经验值'},
  {title: '4、个人页中训练所产生的数据可设置隐藏或向他人展示'},
  {title: '5、训练将统计鉴定量及异鉴率，在鉴定模块点击自己个人信息或在首页右上角点击个人鉴定中心查看，在“我鉴定的”列表中将展示“训”的标识，区分训练与正式数据'},
  {title: '6、本训练营开放给真正想学习鉴定的球友，希望学有所成再去正式表态，才有助于对求鉴者的正确引导'},
])



const changeKnow = () =>{
  hide()
  emit('close')
}


const show = () => {
  precautionsShow.value = true
}
const hide = () => {
  precautionsShow.value = false
}


defineExpose({show, hide})

</script>

<style lang="scss" scoped>
.precautions-popup {
  margin: 15px 12px 20px;
  box-sizing: border-box;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .tips {
    color: #666666;
    font-size: 13px;
    font-weight: 400;
  }

  .content-box {
    font-size: 14px;
    color: #3D3D3D;
    .content-text {
      line-height: 1.4;
      margin-bottom: 20px;
    }
  }
  .footer-btn {
    color: #FFFFFF;
    font-size: 16px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #478E87;
  }
}
</style>