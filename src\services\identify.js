import {get, post, put} from "@/services/request"

const identify = {

    //根据token进行登录
    tokenLogin: (params) => {
        let url = `/ea/login`
        return get({url, params})
    },

    getSearch: (params) => {
        let url = `/ea/search`
        return get({url, params})
    },

    //获取相关装备
    getEquip: (params) => {
        let url = `/ea/statistics/equip`
        return get({url, params})
    },

    //求鉴必看
    getEaHelp: (params) => {
        let url = `/ea/help/thread`
        return get({url, params})
    },

    //获取帖子详情
    getEaDetails: (params) => {
        let url = `/ea/thread`
        return get({url, params})
    },

    //获取今日发帖次数和提升次数 type: 1发帖 2提升
    getEaCount: (params) => {
        let url = `/ea/count`
        return get({url, params})
    },

    //我的鉴定 type: 1我发布的 2我鉴定的
    getMyEaList: (params) => {
        let url = `/ea/my/list`
        return get({url, params})
    },

    //我的好文
    getMySelected: (params) => {
        let url = `/ea/dz/selected`
        return get({url, params})
    },

    //获取帖子总数 type: brand品牌 cate: 品类
    getThreadCount: (params) => {
        let url = `/ea/thread/count`
        return get({url, params})
    },

    //获取我的属性
    getSetting: (params) => {
        let url = `/ea/setting`
        return get({url, params})
    },

    //设置我的属性
    editSetting: (data) => {
        let url = '/ea/setting'
        return post({url, data})
    },

    //获取鉴定列表
    getAppraisal: (params) => {
        let url = `/ea/appraisal`
        return get({url, params})
    },

    //获取榜单
    getRanking: (params) => {
        let url = `/ea/ranking`
        return get({url, params})
    },

    //鉴定真假
    addAppraisal: (data) => {
        let url = `/ea/appraisal`
        return post({url, data})
    },

    //修改鉴定
    editAppraisal: (data) => {
        let url = `/ea/appraisal/regret`
        return post({url, data})
    },

    //查看回帖列表
    getComment: (params) => {
        let url = `/ea/post`
        return get({url, params})
    },

    //获取回帖详情
    getPostDetail: (params) => {
        let url = `/ea/post/id`
        return get({url, params})
    },

    //回复回帖列表
    addComment: (data) => {
        let url = `/ea/post`
        return post({url, data})
    },

    //获取品牌列表
    getBrandList: (params) => {
        let url = `/ea/brand/list`
        return get({url, params})
    },

    //获取品类列表
    getCategoryList: (params) => {
        let url = `/ea/category/list`
        return get({url, params})
    },


    //上传图片
    uploadFile: (id, data) => {
        let url = `/ea/upload?uid=${id}`
        return post({url, data})
    },

    //马甲上传图片
    uploadMap: (data) => {
        let url = `/ea/upload/mag`
        return post({url, data})
    },

    //删除图片
    delImgFile: (data) => {
        let url = `/ea/delete/img`
        return post({url, data})
    },

    //发布鉴定
    addIdentify: (data) => {
        let url = `/ea/thread`
        return post({url, data})
    },

    //发起付款
    startPayment: (data) => {
        let url = `/ea/pay`
        return post({url, data})
    },

    //查询付款状态
    queryPayment: (params) => {
        let url = `/ea/pay/query`
        return get({url, params})
    },


    //补图
    repairIdentify: (data) => {
        let url = `/ea/thread/extra`
        return post({url, data})
    },

    //帖子提升
    upThread: (data) => {
        let url = `/ea/thread/up`
        return post({url, data})
    },

    //屏蔽
    setCensor: (data) => {
        let url = `/ea/censor`
        return post({url, data})
    },

    //推荐帖子
    recommendThread: (data) => {
        let url = `/ea/thread/recommend`
        return post({url, data})
    },

    //点赞主帖
    likeThread: (data) => {
        let url = `/ea/like`
        return post({url, data})
    },


    //获取用户关注列表
    getUserList: (params) => {
        let url = `/ea/user/list`
        return get({url, params})
    },

    //获取关联装备列表
    getEquipList: (params) => {
        let url = `/ea/cbo/eq/list`
        return get({url, params})
    },

    //通过id获取用户
    queryUserList: (params) => {
        let url = `/ea/setting/extra`
        return get({url, params})
    },

    //打开红包
    openRed: (params) => {
        let url = `/ea/red/claim`
        return get({url, params})
    },

    //点赞回复
    // likePost: (data) => {
    //     let url = 'api/forum/post/like'
    //     return post({url, data})
    // },

    // postrule: (data) => {
    //     return request({
    //         method: 'post',
    //         data: data,
    //         url: '/rules'
    //     })
    // },
    //
    //
    // updaterule: (id, data) => {
    //     return request({
    //         method: 'post',
    //         data: data,
    //         url: '/rules/${id}'
    //     })
    // }
}

export default identify
