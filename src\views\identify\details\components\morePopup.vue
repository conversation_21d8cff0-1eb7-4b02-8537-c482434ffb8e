<template>
  <van-popup
      v-model:show="morePopup"
      round
      position="bottom"
  >
    <div class="btn-container">
      <div v-if="userInfo.id === item?.user?.uid && improveType && moreType === 1 && item.display !== -1"
           class="type-btn" @click="handleImprove(item)">推广
      </div>
      <div v-if="userInfo.id === item?.user?.uid && improveType && moreType === 1 && item.display !== -1"
           class="type-line"/>
      <div
          v-if="((moreType === 1 && [1, 2].includes(userInfo.groupId)) || (moreType === 1 && Number(userInfo.id) === 117229))  && !item.recommend"
          class="type-btn" @click="handleRecommend(item)">推荐
      </div>
      <div
          v-if="((moreType === 1 && [1, 2].includes(userInfo.groupId)) || (moreType === 1 && Number(userInfo.id) === 117229)) && item.recommend"
          class="type-btn" @click="handleRecommend(item)">取消推荐
      </div>
      <div
          v-if="(moreType === 1 && [1, 2].includes(userInfo.groupId)) || (moreType === 1 && Number(userInfo.id) === 117229)"
          class="type-line"/>
      <div
          v-if="moreType !== 1 && userInfo.id !== item?.user?.uid"
          class="type-btn" @click="handleReport(item)">举报
      </div>
      <div
          v-if="moreType !== 1 && userInfo.id !== item?.user?.uid"
          class="type-line"/>
      <div
          v-if="((([1, 2].includes(userInfo.groupId) || Number(userInfo.id) === 117229)) && item.display !== -2)"
          class="type-btn" @click="handleShield(item)">屏蔽
      </div>
      <div
          v-if="((([1, 2].includes(userInfo.groupId) || Number(userInfo.id) === 117229)) && item.display === -2)"
          class="type-btn" @click="cancelShield(item)">取消屏蔽
      </div>
      <div
          v-if="([1, 2].includes(userInfo.groupId)) || userInfo.id !== item?.user?.uid"
          class="type-line"/>
      <div
          v-if="(userInfo.id === item?.user?.uid && ( userInfo?.groupId > 10 || [1, 2, 3].includes(userInfo.groupId))) || (userInfo.id === item?.user?.uid && filteredArray.length < 6) || item.display === -1"
          class="type-btn" @click="handleEdit(item)">编辑
      </div>
      <div
          v-if="(userInfo.id === item?.user?.uid && userInfo?.groupId > 10) || (userInfo.id === item?.user?.uid && filteredArray.length < 6) || item.display === -1"
          class="type-line"/>
      <div v-if="moreType !== 1" class="type-btn" @click="handleReply(item)">回复</div>
      <div
          v-if="userInfo.id !== item?.user?.uid || ([1, 2].includes(userInfo.groupId))"
          class="type-line"/>
      <!--      <div v-if="userInfo.id === item?.user?.uid && userInfo?.groupId > 10" class="type-btn" @click="handleDel(item)">-->
      <!--        删除-->
      <!--      </div>-->
      <div class="division"/>
      <div class="type-btn" @click.stop="hide">取消</div>
      <div class="mb-16"></div>
    </div>
  </van-popup>

</template>

<script setup>
import {ref, computed, watch} from "vue";
import {useAuthStore} from "@/stores/auth.js";

const filteredArray = ref(0)

const authStore = useAuthStore();
import {useRouter} from "vue-router";
import {otherList, categoryBrandMap} from '@/views/identify/lists'
import {getCache} from "@/utils/cache.js";

const router = useRouter()

// 用户信息
const userInfo = computed(() => {
  return authStore.userInfo
})


const morePopup = ref(false)
const moreType = ref()
const improveType = ref(true)
// const item = ref()

const emit = defineEmits(['edit', 'del', 'reply', 'shield', 'cancelShield', 'recommend'])


const props = defineProps({
  item: {
    type: Object,
    default: {}
  },
  info: {
    type: Object,
    default: {}
  },
})


const handleRecommend = (item) => {
  hide()
  emit('recommend', item)
}

const today = new Date().toISOString().split('T')[0];

const isOverdue = (time) => {
  if (!time) return false;
  const targetDate = new Date(time);
  const fourteenDaysLater = new Date(targetDate.getTime() + 14 * 24 * 60 * 60 * 1000);
  return new Date(today) > fourteenDaysLater;
};

const handleImprove = () => {
  hide()
  let freetype = isOverdue(props.info?.createTimeOrigin) && ((props.info.fake + props.info.real) >= 3)
  let name = encodeURIComponent(props.info.name)
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/brand/improve?mag_hide_progress=1`, {id: props.info.id, name:name, freetype: freetype});
  } else {
    router.push({
      name: 'improve',
      query: {
        id: props.info.id,
        name:name,
        freetype: freetype
      }
    })
  }
}

const handleEdit = (item) => {
  hide()
  emit('edit', item)
}

const handleShield = (item) => {
  hide()
  emit('shield', {
    item,
    type: moreType.value
  })
}

const cancelShield = (item) => {
  hide()
  emit('cancelShield', {
    item,
    type: moreType.value
  })
}


const handleReply = (item) => {
  hide()
  emit('reply', item)
}

const handleDel = (item) => {
  emit('del', moreType.value)
}

const delConfirm = () => {

}


const mergeArrays = (partial, all) => {
  const result = [];
  partial?.forEach((item, index) => {
    if (all[index] && partial[index].path && !partial[index].extra) {
      result.push({
        name: all[index].name,
        icon: all[index].icon,
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        createTime: partial[index].createTime
      });
    } else if (partial[index].path) {
      result.push({
        name: '其他补充',
        icon: '',
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        createTime: partial[index].createTime
      });
    }
  });
  return result;
};

watch(() => props.info,
    (val) => {
      let list = categoryBrandMap[val?.cateName]?.[val?.brandName] ?? categoryBrandMap[val?.cateName] ?? otherList.value;
      const newList = mergeArrays(val.attachList, list);
      filteredArray.value = newList.filter(item => item.name === "其他补充");
    }, {
      deep: true
    })

const handleReport = (item) => {
  hide()
  let obj = {
    url: `${window.location.href}&pid=${item.id}`
  }
  let encodedUrl = JSON.stringify(obj)
  const curUrl = btoa(encodedUrl);
  if (authStore.phone) {
    window.mag.newWin(`https://app.badmintoncn.com/mag/user/v1/user/userReportView?type=3&type_value=${curUrl}&toUserId=${item.user.uid}`);
  } else {
    window.location.href = `https://app.badmintoncn.com/mag/user/v1/user/userReportView?type=3&type_value=${curUrl}&toUserId=${item.user.uid}`
  }
}

const open = (type) => {
  morePopup.value = true
  moreType.value = type
  let permissionList = JSON.parse(getCache('PERMISSION'))
  // improveType.value = permissionList.release
  console.log('moreType.value ', moreType.value)
}

const hide = () => {
  morePopup.value = false
}

defineExpose({open, hide})

</script>

<style lang="scss" scoped>
.btn-container {
  .type-btn {
    color: #333333;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    padding: 20px 0;
  }

  .type-line {
    margin: 0 20px;
    height: 1px;
    background-color: #F5F5F5;
  }

  .division {
    height: 5px;
    background-color: #F5F5F5;
  }
}
</style>