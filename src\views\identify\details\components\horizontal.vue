<template>
  <div class="horizontal-page" v-if="newList.length">
    <div class="swiper-box">
      <van-swipe @change="onChange" ref="swiperRef" lazy-render>
        <van-swipe-item v-for="(news, index) in newList" :key="item" @click="handlePreview(index)">
          <div class="horizontal-box">
<!--            <img class="horizontal-img" loading="lazy" :src="`${news.path}-bigimg.webp`"/>-->
            <van-image
                class="horizontal-img"
                fit="contain"
                lazy-load
                :src="`${news.path}-bigimg.webp`"
            />
            <img v-if="!news.censor" class="horizontal-icon" :src="violationIcon"/>
          </div>
        </van-swipe-item>
        <template #indicator="{ active, total }">
          <!--          <div class="custom-indicator"></div>-->
          <div class="convert-bot">
            <div :class="{'yellow-text': swiperExtra}">上传于{{ swiperTime }}</div>
            <div>
              <text v-if="swiperExtra" class="mr-10">{{ '补图' }}</text>
              <text>{{ swiperName }}</text>
              <text>{{ active + 1 }}/{{ total }}</text>
            </div>
          </div>
        </template>
      </van-swipe>
      <div class="big-convert-box" @click="handleConvert">
        <div class="convert-box flex-vhc">
          <img class="convert-icon" :src="convertIcon"/>
        </div>
      </div>

    </div>
    <div class="scroll-container" ref="scrollContainer" @scroll="handleScroll">
      <div class="scroll-content" ref="scrollContent">
        <div v-for="(item, index) in newList" :key="index"
             class="scroll-list" @click="handleChange(index)">
          <div class="item-img-box" :class="{'active-scroll-list': checkImage === index}">
<!--            <img class="item-img" loading="lazy" :src="`${item.path}?imageView2/1/w/84/h/84/q/75/format/webp`"/>-->
            <van-image
                class="item-img"
                fit="cover"
                lazy-load
                :src="`${item.path}?imageView2/1/w/84/h/84/q/75/format/webp`"
            />
            <img v-if="!item.censor" class="item-img-icon" :src="violationIcon"/>
            <div class="cover-box">{{ item.extra ? '补图 (其他)' : item.name}}</div>
          </div>
        </div>
      </div>
    </div>
    <!--    <div class="flex-vhc">-->
    <!--      <div class="progress-bar">-->
    <!--        <div class="progress-fill" :style="{ marginLeft : progressWidth + 'px' }"></div>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<script setup>
import {ref, onMounted, onBeforeUnmount, watch} from "vue";
import {otherList, categoryBrandMap} from '@/views/identify/lists'
import convertIcon from "@/assets/images/details/convertIcon.png";
import violationIcon from "@/assets/images/common/violation.png"
import {useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();

const newList = ref([]);
const swiperTime = ref('')
const swiperName = ref('')
const swiperExtra = ref(false)
const swiperRef = ref();
const checkImage = ref(0);
const progressWidth = ref(0);
const scrollContainer = ref(null);
const scrollContent = ref(null);
const progressBarWidth = 18; // 进度条容器总宽度
const progressFillWidth = 9; // 滑块宽度


const props = defineProps({
  item: {
    type: Object,
    default: () => {
    }
  }
})

const emit = defineEmits(['change', 'backTop'])

const handlePreview = (index) => {
  let imgs = []
  newList.value.forEach((item) => {
    imgs.push(`${item.path}-bigimg.webp`)
  })
  if(authStore.phone ) {
    window.mag.previewImage({
      current: index,
      pics: imgs
    });
  } else {
    showImagePreview({
      images: imgs,
      startPosition: index,
    });
  }
}

const handleScroll = () => {
  const container = scrollContainer.value;
  const content = scrollContent.value;

  if (!container || !content) return;

  const scrollLeft = container.scrollLeft;
  const containerWidth = container.offsetWidth;
  const contentWidth = content.scrollWidth; // 使用scrollWidth获取完整内容宽度

  const maxScroll = contentWidth - containerWidth;

  if (maxScroll > 0) {
    // 计算滑块可移动范围：总宽度 - 滑块宽度
    const availableSpace = progressBarWidth - progressFillWidth;
    // 根据滚动比例计算滑块位置
    const ratio = scrollLeft / maxScroll;
    progressWidth.value = ratio * availableSpace;
  } else {
    progressWidth.value = 0;
  }
};

const handleConvert = () => {
  emit('change')
};

const handleChange = (index) => {
  emit('backTop')
  // 计算当前选中的 item 在 scroll-content 中的位置
  const itemElement = scrollContent.value.children[index];
  if (itemElement) {
    const itemRect = itemElement.getBoundingClientRect();
    const containerRect = scrollContainer.value.getBoundingClientRect();

    // 如果当前选中的 item 不在可视区域内，则滚动到该 item
    if (itemRect.right > containerRect.right || itemRect.left < containerRect.left) {
      scrollContainer.value.scrollTo({
        left: itemElement.offsetLeft - (containerRect.width / 2) + (itemRect.width / 2),
        behavior: 'smooth'
      });
    }
  }

  // 同步 swiper 的状态
  swiperRef.value.swipeTo(index)
  checkImage.value = index
};

const onChange = (index) => {
  checkImage.value = index;
  swiperName.value = newList.value[index].name
  swiperTime.value = newList.value[index].createTime
  swiperExtra.value = newList.value[index].extra

  // 计算当前选中的 item 在 scroll-content 中的位置
  const itemElement = scrollContent.value.children[index];
  if (itemElement) {
    const itemRect = itemElement.getBoundingClientRect();
    const containerRect = scrollContainer.value.getBoundingClientRect();

    // 如果当前选中的 item 不在可视区域内，则滚动到该 item
    if (itemRect.right > containerRect.right || itemRect.left < containerRect.left) {
      scrollContainer.value.scrollTo({
        left: itemElement.offsetLeft - (containerRect.width / 2) + (itemRect.width / 2),
        behavior: 'smooth'
      });
    }
  }
};

// const mergeArrays = (all, partial) => {
//   const result = [];
//   all.forEach((item, index) => {
//     if (partial[index] && partial[index].path) {
//       // 如果有对应的项，则合并数据
//       result.push({
//         name: item.name,
//         icon: item.icon,
//         require: item.require,
//         id: partial[index].id,
//         path: partial[index].path,
//         createTime: partial[index].createTime
//       });
//     } else {
//       // 如果没有对应的项，则只添加原始数据
//       // result.push(item);
//     }
//   });
//   return result;
// };

const mergeArrays = (partial, all) => {
  const result = [];
  partial.forEach((item, index) => {
    if (all[index] && partial[index].path && !partial[index].extra) {
      result.push({
        name: all[index].name,
        icon: all[index].icon,
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        censor: partial[index].censor,
        createTime: partial[index].createTime
      });
    } else if (partial[index].path) {
      result.push({
        name: '其他补充',
        icon: '',
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        path: partial[index].path,
        censor: partial[index].censor,
        createTime: partial[index].createTime
      });
    }
  });
  return result;
};

watch(() => props.item,
    (val) => {
      let list = categoryBrandMap[val?.cateName]?.[val?.brandName] ?? categoryBrandMap[val?.cateName] ?? otherList.value;
      // const mergedArray = mergeArrays(list, val.attachList);
      // newList.value = mergeArrays(list, val.attachList);
      newList.value = mergeArrays(val.attachList, list);
      swiperName.value = newList.value[0]?.name
      swiperTime.value = newList.value[0]?.createTime
      swiperExtra.value = newList.value[0]?.extra
      console.log('newList.value', newList.value)
    }, {
      deep: true,
      immediate: true
    })

onMounted(() => {
  // 初始化时获取正确的尺寸
  setTimeout(() => {
    handleScroll();
  }, 0);

  // 监听窗口大小变化，以适应不同屏幕尺寸
  window.addEventListener('resize', handleScroll);
});

onBeforeUnmount(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleScroll);
});
</script>

<style scoped lang="scss">
.horizontal-page {
  .swiper-box {
    position: relative;

    .horizontal-box {
      position: relative;

      .horizontal-img {
        object-fit: contain;
        width: 375px;
        height: 488px;
        //background-color: #FFFFFF;
        background-color: #030303;
      }

      .horizontal-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 121px;
        height: 121px;
      }
    }

    .big-convert-box{
      position: absolute;
      top: 0;
      right: 0px;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-around;
    }
    .convert-box {
      width: 32px;
      height: 32px;
      border-radius: 23px;
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      background: rgba(88,88,88,0.3);
      .convert-icon {
        width: 18px;
        height: 18px;
      }
    }

    .convert-bot {
      position: absolute;
      bottom: 5px;
      right: 0;
      left: 0;
      display: flex;
      justify-content: space-between;
      padding: 6px 12px;
      color: #FFFFFF;
      font-size: 12px;
      font-weight: 400;
      //background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      background: rgba(88,88,88,0.3);

      .yellow-text {
        color: #FF8F1F;
      }
    }
  }

  .scroll-container {
    overscroll-behavior-y: auto !important;
    margin: 12px;
    display: flex;
    overflow-y: hidden;
    overflow-x: auto; /* 隐藏原生滚动条 */
    -ms-overflow-style: none; /* IE 和 Edge */
    scrollbar-width: none; /* Firefox */
    .scroll-container::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .scroll-content {
      display: flex;
      width: 100%;
      gap: 10px;

      .scroll-list {
        //width: 100%;
        min-width: calc((100% - 25px) / 4); /* 确保最小宽度 */
        //flex-shrink: 0;
        color: #666666;
        //border: 1px dashed #A5A5A5;
        display: flex;
        flex-direction: column;
        //padding: 11px 0;
        align-items: center;
        justify-content: center;

        .item-icon {
          width: 44px;
          height: 44px;
        }

        .item-name {
          padding-top: 8px;
          font-size: 11px;
          font-weight: 400;
        }
      }

      .item-img-box {
        position: relative;
        width: 84px;
        height: 84px;

        .item-img {
          object-fit: cover;
          width: 84px;
          height: 84px;
          background-color: #FFFFFF;
        }

        .item-img-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 50px;
          height: 50px;
        }
        .cover-box {
          z-index: 2;
          position: absolute;
          bottom: 0;
          color: #FFFFFF;
          font-size: 11px;
          width: 100%;
          height: 18px;
          line-height: 18px;
          text-align: center;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          background: rgba(0,0,0,0.2);
        }
      }

      position: relative;

      .horizontal-img {
        object-fit: contain;
        width: 375px;
        height: 488px;
        background-color: #FFFFFF;
      }

      .horizontal-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 121px;
        height: 121px;
      }

      //.active-scroll-list {
      //  //color: #478E87;
      //  //background-color: #DFF7F6;
      //  //border: 1px dashed #478E87;
      //  //border: 1px solid #478E87;
      //}
      .active-scroll-list {
        position: relative;
      }

      .active-scroll-list::before {
        z-index: 1;
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(71, 142, 135, 0.7);
        pointer-events: none;
      }
    }
  }

  /* 修改进度条样式 */
  .progress-bar {
    position: relative; /* 添加相对定位 */
    height: 5px;
    width: 18px;
    background: #EBEBEB;
    border-radius: 2px;
    overflow: visible; /* 允许滑块溢出显示 */
  }

  .progress-fill {
    position: absolute; /* 改为绝对定位 */
    left: 0;
    height: 100%;
    width: 9px;
    background-color: #478E87;
    border-radius: 2px;
    transition: margin-left 0.2s ease; /* 添加平滑过渡 */
  }
}
</style>