<template>
  <div class="data-statistics-page" :style="{ paddingTop: `${titleHeight}px`}">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      :style="{ height: `calc(100vh - 10px - ${titleHeight}px)`}">
      <div class="statistics-container">
        <div class="all-container">
          <div class="all-count-box">
            <div class="all-count">{{ recentData?.allNum }}</div>
            <div>近期求鉴</div>
          </div>
          <div class="all-classify">
            <div class="all-jd-box">
              <div class="jd-icon">
                <img class="icon" src="https://www.badmintoncn.com/cbo_ea/img/pc/common/<EMAIL>"/>
              </div>
              <div class="jd-text-box">
                <div class="text-count">{{ recentData?.appraisalNum }}</div>
                <div>已被鉴定</div>
              </div>
            </div>
            <div class="vertical-line"></div>
            <div class="all-jd-box">
              <div class="jd-icon">
                <img class="icon" src="https://www.badmintoncn.com/cbo_ea/img/pc/common/<EMAIL>"/>
              </div>
              <div class="jd-text-box">
                <div class="text-count">{{ recentData?.recent?.real }}</div>
                <div>鉴定看正</div>
              </div>
            </div>
            <div class="vertical-line"></div>
            <div class="all-jd-box">
              <div class="jd-icon">
                <img class="icon" src="https://www.badmintoncn.com/cbo_ea/img/pc/common/<EMAIL>"/>
              </div>
              <div class="jd-text-box">
                <div class="text-count">{{ recentData?.recent?.fake }}</div>
                <div>鉴定看假</div>
              </div>
            </div>
          </div>
        </div>
        <div class="brand-container">
          <div class="brand-title">近期各品牌数据统计</div>
          <div style="height: 1px; background-color: #F5F5F5"></div>
        </div>
        <div class="brand-list-container">
          <div class="brand-list-box" v-for="item in brandDataList">
            <div class="brand-list-top" @click="handleExpand(item)">
              <div class="brand-left">
                <img class="brand-icon" :src="item.logo"/>
                <div class="brand-text">
                  <div class="brand-count">{{ `鉴定总数${item?.stats.all?.real + item?.stats.all?.fake}` }}</div>
                  <div>
                    {{ `看正${item?.stats.all?.real}｜看假${item?.stats.all?.fake}｜看假率${item?.stats.all?.rate}%` }}
                  </div>
                </div>
              </div>
              <div class="brand-right">
                {{ item.ename === expandBrand ? '收起' : '展开' }}
                <van-icon :name="item.ename === expandBrand ? 'arrow-down' : 'arrow'" size="11"/>
              </div>
            </div>
            <div v-if="item.ename === expandBrand" class="brand-circle-box">
              <div
                  v-for="(config) in brandConfig"
                  :key="config.dataKey"
                  class="brand-item"
              >
                <div class="brand-item-box">
                  <div class="item-box-left">
                    <div class="brand-left-box">
                      <img class="left-logo" :src="config.logo"/>
                      <div class="left-text">
                        {{ config.name }}
                        {{
                          (item?.stats?.[config.dataKey]?.fake || 0) +
                          (item?.stats?.[config.dataKey]?.real || 0)
                        }}
                      </div>
                    </div>
                    <div class="brand-item-count">
                      {{
                        `看正${item?.stats?.[config.dataKey]?.real || 0}｜看假${item?.stats?.[config.dataKey]?.fake || 0}`
                      }}
                    </div>
                  </div>
                  <div class="item-box-right">
                    <client-only>
                      <v-chart
                          v-if="VChart"
                          :option="getCircleChartOption(item?.stats?.[config.dataKey])"
                          style="width: 54px; height: 54px;"
                      />
                    </client-only>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="item.ename === expandBrand">
              <div v-if="item.ename === expandBrand" class="brand-year-box">
                <div class="brand-year-btn" @click="handleYear">
                  <div>近半年数据图</div>
                  <img class="btn-icon" :src="item.ename === expandBrand && yearChart ? bottomIcon :rightIcon"/>
                </div>
              </div>
              <div v-if="item.ename === expandBrand && yearChart">
                <div class="chart-summary">{{
                    `共鉴定${(currentBrandHistoryData.fake || 0) + (currentBrandHistoryData?.real || 0)}｜看正${currentBrandHistoryData?.real || 0}｜看假${currentBrandHistoryData?.fake || 0}
    ｜看假率${currentBrandHistoryData?.rate || 0}%`
                  }}
                </div>
                <div class="yearly-chart">
                  <v-chart v-if="VChart" :option="yearlyChartOption" style="width: 100%; height: 280px;"/>
                </div>
              </div>
            </div>
            <div style="height: 1px; width: 100%; background-color: #F5F5F5; margin-top: 20px"></div>
          </div>
        </div>
      </div>
    </van-pull-refresh>
<!--    <Tabbar :page="3"/>-->
  </div>
</template>
<script setup>
import {ref, onMounted, computed } from "vue";
import {useAuthStore} from "@/stores/auth.js";
import commonApi from "@/services/common";
import TabBar from "@/components/tabbar.vue";
import rightIcon from "@/assets/images/common/ysj.png"
import bottomIcon from "@/assets/images/common/dsj.png"
import {use} from "echarts/core";
import {PieChart, BarChart, LineChart} from 'echarts/charts'
import {LabelLayout, UniversalTransition} from "echarts/features";
import {CanvasRenderer} from "echarts/renderers";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from "vue-echarts";
import {getQueryParam} from "@/utils/common.js";
import {useRoute} from "vue-router";

use([
  PieChart,
  BarChart,
  LineChart,
  CanvasRenderer,
  LabelLayout,
  UniversalTransition,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent]);

const authStore = useAuthStore();

const route = useRoute()


const refreshing = ref(false)
const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const recentData = ref({})
const historyData = ref([])
const brandId = ref(1)
const expandBrand = ref('yonex')
const yearChart = ref(false)
const brandDataList = ref([])
const brandList = ref([
  {
    id: 1,
    name: "尤尼克斯",
    ename: "yonex",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/yonex.png"
  },
  {
    id: 22,
    name: "李宁",
    ename: "lining",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/lining.png"
  },
  {
    id: 2,
    name: "威克多",
    ename: "victor",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/victor.png"
  },
  {
    id: 0,
    name: "其他",
    ename: "other",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/<EMAIL>"
  },
])
const brandConfig = [
  {name: '球拍', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymqp.png", dataKey: 'racket'},
  {name: '球鞋', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymqx.png", dataKey: 'sneakers'},
  {name: '羽毛球', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymq.png", dataKey: 'birdie'},
  {name: '球拍线', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymqpx.png", dataKey: 'line'},
  {name: '其他', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/qt.png", dataKey: 'other'},
];


const handleExpand = (item) => {
  yearChart.value = false
  if (item.ename === expandBrand.value) {
    expandBrand.value = ''
  } else {
    brandId.value = item.id
    expandBrand.value = item.ename
  }
  window.mag.setData({
    shareData: {
      title: `中羽装备鉴定数据统计`,
      des: `主流品牌相关鉴定数据展示`,
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      // linkurl: `${window.location.href}`,
      linkurl: `${window.location.href}&brandId=${brandId.value}`,
    }
  });
}

const handleYear = () => {
  yearChart.value = !yearChart.value
}

// 圆形图表配置
const getCircleChartOption = (item) => {
  return {
    title: [
      {
        text: `{a|${item?.rate || 0}}{b|%}`,
        left: "42%",
        top: "30%",
        textAlign: "center",
        textVerticalAlign: "middle",
        textStyle: {
          fontSize: 14,
          color: "#3D3D3D",
          lineHeight: 14,
          fontWeight: '600',
          rich: {
            a: {
              fontSize: 14,
              fontWeight: '600'
            },
            b: {
              fontSize: 8, // 设置 % 更小的字体
              fontWeight: '600',
              verticalAlign: 'bottom', // 可选：让 % 靠下对齐
              padding: [0, 0, 0, 0] // 微调位置
            }
          }
        },
      },
      {
        text: "看假率",
        left: "42%",
        top: "58%",
        textAlign: "center",
        textVerticalAlign: "middle",
        textStyle: {
          fontSize: 10,
          color: "#3D3D3D",
          lineHeight: 10,
          fontWeight: '400'
        },
      },
    ],
    series: [{
      type: 'pie',
      radius: ['100%', '80%'],
      center: ['50%', '50%'],
      data: [
        {value: item?.real || 0, itemStyle: {color: '#00B578'}},
        {value: item?.fake || 0, itemStyle: {color: '#FA5151'}},
      ],
      label: {show: false},
      emphasis: {disabled: true}
    }]
  }
}

//柱状图配置
const yearlyChartOption = computed(() => {
  const brandData = currentBrandHistoryData?.value?.history;
  const realData = brandData?.map(item => item.real || 0);
  const fakeData = brandData?.map(item => item.fake || 0);
  const rateData = brandData?.map(item => {
    const rate = item.rate;
    return rate === "" ? 0 : parseFloat(rate);
  });
  const xAxisData = Array.from({length: brandData?.length}, (_, i) => String(i + 1));
  return {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: '#fff',
      borderWidth: 0,
      padding: [10, 15],
      textStyle: {
        color: '#1D2129',
        fontSize: 12
      },
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border-radius: 8px;',
      formatter: function (params) {
        const dataIndex = params[0].dataIndex;
        const currentDataPoint = brandData[dataIndex];
        const weekFrom = currentDataPoint?.weekFrom ?? '';
        const weekTo = currentDataPoint?.weekTo ?? '';
        const weekText = `本组统计第${weekFrom}~${weekTo}周的数据`;

        let html = `<div style="margin-bottom: 8px; color: #999999; font-size: 10px; font-weight: 400; display: flex; align-items: center">
<img style="width:12px; height: 12px; margin-right: 3px" src="https://www.badmintoncn.com/cbo_ea/img/pc/common/<EMAIL>"/>
<div>${weekText}</div>
</div>`;
        params.forEach(item => {
          let value = item.value;
          let label = item.seriesName;
          if (label === '看假率') {
            value = `${value}%`;
          }
          const color = item.color;
          const dot = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:8px;height:8px;background-color:${color}"></span>`;
          html += `
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
              ${dot}
              <span style="flex: 1; min-width: 60px;">${label}：</span>
              <span style="font-weight: 500;">${value}</span>
            </div>
          `;
        });

        return html;
      }
    },
    legend: {
      x: '20%',
      top: '13%',
      icon: 'circle',
      borderCap: 'round',
      textStyle: {
        color: '#1D2129',
      },
      data: ['看正', '看假', '看假率']
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {show: false},
      axisTick: {show: false}
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        axisLine: {show: false},
        axisTick: {show: false},
        splitLine: {show: true, lineStyle: {color: '#E5E6EB', type: 'dashed'}}
      },
      {
        type: 'value',
        position: 'right',
        axisLine: {show: false},
        axisTick: {show: false},
        splitLine: {show: false}
      }
    ],
    series: [
      {
        name: '看正',
        type: 'bar',
        stack: 'total',
        barWidth: '40%',
        data: realData,
        itemStyle: {color: '#00B578'}
      },
      {
        name: '看假',
        type: 'bar',
        stack: 'total',
        barWidth: '40%',
        data: fakeData,
        itemStyle: {color: '#FA5151'}
      },
      {
        name: '看假率',
        type: 'line',
        yAxisIndex: 1,
        symbolSize: 9,
        data: rateData,
        itemStyle: {color: '#165DFF'},
        lineStyle: {color: '#165DFF'}
      }
    ]
  };
});

const onRefresh = async () => {
  // 重新刷新当前页面数据
  refreshing.value = false
  await getRecentData()
  await getHistoryData()
}

const getRecentData = async () => {
  let res = await commonApi.getStatistics({zone: 'recent'})
  if (res.code === 200) {
    recentData.value = res.data.list
    brandDataList.value = brandList.value.map(brand => {
      const stats = recentData.value[brand.ename] || {}; // 根据 ename 匹配，找不到用空对象
      return {
        ...brand,
        stats
      };
    });
  }
  // console.log('brandDataList', brandDataList.value)
}

const currentBrandEname = computed(() => {
  const brand = brandList.value.find(item => item.id === Number(brandId.value));
  return brand ? brand.ename : 'yonex'; // 默认 yonex
});

const currentBrandHistoryData = computed(() => {
  return historyData.value?.[currentBrandEname.value] || [];
});

const getHistoryData = async () => {
  let res = await commonApi.getStatistics({zone: 'history'})
  if (res.code === 200) {
    historyData.value = res.data.list
    console.log(' historyData.value', historyData.value)
  }
}


onMounted(async () => {
  let type = route.query.brandId || getQueryParam('brandId') || 1
  brandId.value = Number(type)
  expandBrand.value = brandList.value.find(item => item.id ===  brandId.value)?.ename ?? 'yonex';
  await getRecentData()
  await getHistoryData()
  window.mag.setData({
    shareData: {
      title: `中羽装备鉴定数据统计`,
      des: `主流品牌相关鉴定数据展示`,
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      // linkurl: `${window.location.href}`,
      linkurl: `${window.location.href}&brandId=${brandId.value}`,
    }
  });
})

if (authStore.phone) {
  window.mag.setTitle('近期数据统计');
  window.mag.setNavigationColor('#8629FF');
  window.mag.setNavigationBarStyle({
    navi_style:1, // 白色导航栏文字
  });
} else {
  document.title = '近期数据统计'
}



window.mag.showNavigation()
</script>

<style lang="scss" scoped>
.data-statistics-page {
  position: relative;
  //height: 100vh;
  height: 100%;
  width: 100vw;
  background-color: #FFFFFF;

  .content-table-list {
    overflow: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .statistics-container {
    .all-container {
      color: #FFFFFF;
      padding: 5px 16px 26px;
      height: 126px;
      background: linear-gradient(180deg, #8629FF 0%, #5D8DFF 100%);

      .all-count-box {
        margin-top: 10px;
        display: flex;
        align-items: baseline;
        font-size: 11px;
        font-weight: 400;

        .all-count {
          font-size: 30px;
          font-weight: 700;
          margin-right: 6px;
        }
      }

      .all-classify {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 26px;

        .all-jd-box {
          display: flex;
          align-items: center;

          .jd-icon {
            position: relative;
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.9);

            .icon {
              width: 24px;
              height: 24px;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }

          .jd-text-box {
            font-size: 11px;
            font-weight: 400;
            margin-left: 5px;

            .text-count {
              font-size: 18px;
              font-weight: 500;
              margin-bottom: 4px;
            }
          }
        }
      }

      .vertical-line {
        height: 34px;
        width: 1px;
        background-color: #FFFFFF;
        opacity: 0.1;
      }
    }

    .brand-container {
      margin-top: -30px;
      border-radius: 15px 15px 0 0;
      background-color: #FFFFFF;

      .brand-title {
        color: #3D3D3D;
        font-size: 16px;
        font-weight: 600;
        padding: 15px 12px 12px;
      }
    }

    .brand-list-container {
      margin: 0 12px;
      padding-bottom: 20px;

      .brand-list-box {
        margin: 20px 0;

        .brand-list-top {
          display: flex;
          justify-content: space-between;

          .brand-left {
            display: flex;
            align-items: center;

            .brand-icon {
              width: 72px;
              height: 40px;
              border-radius: 5px;
            }

            .brand-text {
              color: #666666;
              font-size: 12px;
              font-weight: 400;
              margin-left: 10px;

              .brand-count {
                color: #3D3D3D;
                font-size: 14px;
                font-weight: 400;
                margin-bottom: 6px;
              }
            }
          }

          .brand-right {
            color: #999999;
            font-size: 11px;
            font-weight: 400;
          }
        }

        .brand-circle-box {
          margin-top: 20px;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          column-gap: 20px;
          row-gap: 20px;

          .brand-item {
            width: calc((100% - 20px) / 2);

            .brand-item-box {
              display: flex;
              align-items: center;

              .item-box-left {
                //margin-right: 15px;
                margin-right: 4px;

                .brand-left-box {
                  display: flex;
                  align-items: center;
                  width: 104px;

                  .left-logo {
                    width: 16px;
                    height: 16px;
                  }

                  .left-text {
                    font-weight: 500;
                    font-size: 13px;
                    margin-left: 4px;
                  }

                }
              }

              .brand-item-count {
                color: #666666;
                font-size: 11px;
                font-weight: 400;
                margin-top: 9px;
              }
            }
          }
        }

        .brand-year-box {
          display: flex;
          align-items: center;
          justify-content: center;

          .brand-year-btn {
            margin-top: 20px;
            color: #666666;
            font-size: 14px;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #EDEDED;
            border-radius: 17px;
            padding: 10px 15px;

            .btn-icon {
              width: 14px;
              height: 14px;
            }
          }
        }

        .chart-summary {
          display: flex;
          justify-content: center;
          margin-top: 20px;
          margin-bottom: -18px;
          color: #3D3D3D;
          font-size: 13px;
          font-weight: 400;
        }
      }
    }

  }
}
</style>