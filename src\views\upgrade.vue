<template>
 <div class="upgrade-page" :style="{ paddingTop: `${titleHeight}px`}">
    <Empty class="empty-wrap" tips="系统正在升级中" tips2="请稍后"/>
  </div>
</template>

<script setup>
import {useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();


const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;


if(authStore.phone) {
  window.mag.setTitle('装备鉴定');
} else {
  document.title = '装备鉴定'
}

window.mag.hideMore()

</script>

<style lang="scss" scoped>
.upgrade-page{
 .empty-wrap {
   position: absolute;
   top: 40%;
   left: 50%;
   transform: translate(-50%, -50%);
 }
}

</style>