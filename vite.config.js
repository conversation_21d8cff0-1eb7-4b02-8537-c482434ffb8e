import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import pxtorem from 'postcss-pxtorem'
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from '@vant/auto-import-resolver';
import { resolve } from 'path'
// https://vitejs.dev/config/


const INVALID_CHAR_REGEX = /[\x00-\x1F\x7F<>*#"{}|^[\]`;?:&=+$,]/g;
const DRIVE_LETTER_REGEX = /^[a-z]:/i;
const Timestamp = new Date().getTime();

export default defineConfig({
  // base: '/cbo_ea/dist',
  base: '/cbo_ea_dev/dist',
  plugins: [
    vue(),
    AutoImport({
      resolvers: [VantResolver()],
    }),
    Components({
      resolvers: [VantResolver()],
    }),
  ],
  css: {
    postcss: {
      plugins: [
        pxtorem({
          rootValue: 37.5, // 设计稿宽度为750时，rootValue设为75
          propList: ['*'], // 需要转换的属性
        }),
      ],
    },
    extract: {
      filename: `css/[name].${Timestamp}.css`,
      chunkFilename: `css/[name].${Timestamp}.css`,
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@components': resolve(__dirname, './src/components')
    }
  },
  server: {
    // host: '0.0.0.0',
    // port: 3000,
    host: '**************',
    port: 8000,
    strictPort: true,
    // proxy: {
    //   '/api': {
    //     // target: 'https://go.badmintoncn.com/api',  //正式环境
    //     target: 'https://cbo.badmintoncn.com',  //测试环境
    //     changeOrigin: true,
    //     rewrite: (path) => path.replace(new RegExp('^/api'), '')
    //   }
    // }
  },
  build: {
    rollupOptions: {
      output: {
        // https://github.com/rollup/rollup/blob/master/src/utils/sanitizeFileName.ts
        sanitizeFileName(fileName) {
          const match = DRIVE_LETTER_REGEX.exec(fileName);
          const driveLetter = match ? match[0] : "";
          return (
              driveLetter +
              fileName.slice(driveLetter.length).replace(INVALID_CHAR_REGEX, "")
          );
        },
      },
    },
  },
  configureWebpack: {
    output: { // 输出重构 打包编译后的文件名称 【输出文件夹/文件名(name为默认文件名非参数).时间戳】
      filename: `js/[name].${Timestamp}.js`,
      chunkFilename: `js/[name].${Timestamp}.js`
    },
  }
})
