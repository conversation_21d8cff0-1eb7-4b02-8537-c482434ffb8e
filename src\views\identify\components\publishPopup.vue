<template>
  <van-popup
      v-model:show="showPopup"
      round
      :close-on-click-overlay="false"
  >
    <div class="show-container flex-vhc">
      <img class="send-icon" :src="sendIcon"/>
      <div class="text-df pt-2">已发布待鉴定</div>
      <div class="text-sm pt-8">求鉴已发布，待热心球友鉴定</div>
      <!--      @click="handleJump"-->
      <div class="show-btn mt-28" >{{ `我知道了（${second}S）` }}</div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from "vue";
import sendIcon from "@/assets/images/common/sendIcon.png";

const showPopup = ref(false);
const second = ref(3);
const intervalId = ref(null); // 改为响应式变量



const emit = defineEmits(['jump'])


const handleJump = () => {
  emit('jump');
  clearTimer();

  showPopup.value = false;
}

const startInterval = () => {
  clearTimer(); // 先清除旧定时器
  intervalId.value = setInterval(() => {
    if (second.value > 0) {
      second.value--;
    } else {
      emit('jump');
      // hide(true); // 添加参数区分自动关闭
    }
  }, 1000);
};


const clearTimer = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
};

const open = () => {
  showPopup.value = true;
  second.value = 3;
  startInterval();
};

const hide = (isAuto = false) => {
  showPopup.value = false;
  clearTimer();
  // if (isAuto) {
  //   emit('jump'); // 自动关闭时触发跳转
  // }
};

// onMounted(() => {
//   startInterval();
// });

onUnmounted(() => {
  clearTimer();
});

defineExpose({open, hide});
</script>

<style scoped lang="scss">
.show-container {
  margin: 20px 0;
  color: #3D3D3D;
  display: flex;
  flex-direction: column;

  .send-icon {
    width: 151px;
    height: 151px;
    padding: 0 76px 0 78px;
  }

  .show-btn {
    color: #FFFFFF;
    font-size: 16px;
    padding: 11px 70px;
    text-align: center;
    border-radius: 30px;
    background-color: #478E87;
  }
}
</style>