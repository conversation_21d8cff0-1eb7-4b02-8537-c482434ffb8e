<template>
  <div class="other-user">
    <div class="top-user">
      <div class="user-box" @click="handleUser">
        <div class="avatar-box">
          <img class="avatar-img" :src="userMes?.user?.avatar"/>
        </div>
        <div class="user-desc">
          <div>{{ userMes?.user?.name }}</div>
          <div class="desc-box">
            <GradeTag :group-id="userMes?.user?.groupId" :uid="userMes?.user?.uid"
                      :userLevel="userMes?.user?.userLevel"/>
            <div v-if="userMes?.train" class="train-box">
              <img class="jd-icon" :src="purpleShieldIcon"/>
              <div class="jd-text">训练中</div>
            </div>
          </div>
          <div class="desc" v-if="userMes?.user?.brand">{{ `擅长：${userMes?.user?.brand}` }}</div>
        </div>
      </div>
      <div v-if="myUser" class="grade-btn" @click="handleGrade">等级</div>
    </div>
    <!--    <div class="statistics-box">-->
    <!--      <div class="box-item">-->
    <!--        <div class="item-tag">近期</div>-->
    <!--        <div class="item-number">-->
    <!--          <div>{{ `${Math.floor(userMes?.diff * 100) / 100}%` }}</div>-->
    <!--          <div class="center-text">异鉴率</div>-->
    <!--          <div class="bottom-text">{{ `鉴定量 ${userMes?.count}` }}</div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div class="box-item">-->
    <!--        <div class="item-tag add-up-tag">累计</div>-->
    <!--        <div class="item-number">-->
    <!--          <div>{{ `${Math.floor(userMes?.diffAll * 100) / 100}%` }}</div>-->
    <!--          <div class="center-text">异鉴率</div>-->
    <!--          <div class="bottom-text">{{ `鉴定量 ${userMes?.countAll}` }}</div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div class="box-item"-->
    <!--           v-if=" (myUser && userMes?.countTrain) || (userMes?.train && userMes?.countTrain) || (userMes?.openTrain && userMes?.countTrain)">-->
    <!--        <div class="item-tag train-tag">训练</div>-->
    <!--        <div class="item-number">-->
    <!--          <div>{{ userMes?.openTrain || myUser ? `${Math.floor(userMes?.diffTrain * 100) / 100}%` : `*%` }}</div>-->
    <!--          <div class="center-text">异鉴率</div>-->
    <!--          <div class="bottom-text">{{ userMes?.openTrain || myUser ? `鉴定量 ${userMes?.countTrain}` : '鉴定量 ***' }}-->
    <!--          </div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </div>-->
    <div class="statistics-box">
      <div class="box-item" @click="handleToast('近期')">
        <div class="item-tag">近期</div>
        <div class="item-img-content">
          <div class="item-img-box"
               :class="{'item-img-grey': Number(userMes?.count) === 0, 'item-img-red': Number(userMes?.diff) > 0}  ">
            <div class="pt-18">
              <div>{{ Number(userMes?.count) === 0 ? `-` : `${userMes?.diff || 0}` }}<span
                  style="font-size: 12px">%</span></div>
              <div class="diff-text">异鉴率</div>
            </div>
          </div>
          <div class="first-box" v-if="userMes?.countFirstRecent">
            <div class="first-text">{{ `首鉴${formatWan(userMes?.countFirstRecent) || 0}` }}</div>
            <div class="first-line">|</div>
            <div class="first-text" v-if="userMes?.count !== undefined && userMes?.countFirstRecent !== undefined">
              占{{ getPercentage(userMes?.countFirstRecent, userMes?.count) }}
            </div>
          </div>
          <div class="count-text" :class="{'pt-9': userMes?.countFirstRecent === 0}">
            {{ userMes?.count ? `鉴定量 ${formatWan(userMes.count) || 0}` : '未鉴定' }}
          </div>
          <div v-if="!userMes?.countFirstRecent" style="height: 14px"></div>
        </div>
      </div>
      <div class="box-item" @click="handleToast('总计')">
        <div class="item-tag add-up-tag">总计</div>
        <div class="item-img-content">
          <div class="item-img-box"
               :class="{'item-img-grey': Number(userMes?.countAll) === 0, 'item-img-red': Number(userMes?.diffAll) > 0}">
            <div class="pt-18">
              <div>{{ Number(userMes?.countAll) === 0 ? `-` : `${userMes?.diffAll || 0}` }}<span
                  style="font-size: 12px">%</span></div>
              <div class="diff-text">异鉴率</div>
            </div>
          </div>
          <div class="first-box" v-if="userMes?.countFirstAll">
            <div class="first-text">{{ `首鉴${formatWan(userMes?.countFirstAll) || 0}` }}</div>
            <div class="first-line">|</div>
            <div class="first-text" v-if="userMes?.countAll !== undefined && userMes?.countFirstAll !== undefined">
              占{{ getPercentage(userMes?.countFirstAll, userMes?.countAll) }}
            </div>
          </div>
          <div class="count-text" :class="{'pt-9': userMes?.countFirstAll === 0}">
            {{ userMes?.countAll ? `鉴定量 ${formatWan(userMes?.countAll) || 0}` : '未鉴定' }}
          </div>
          <div v-if="!userMes?.countFirstAll" style="height: 14px"></div>
        </div>
      </div>
      <div class="box-item" @click="handleToast('训练')">
        <div class="item-tag train-tag">训练</div>
        <div class="item-img-content">
          <div class="item-img-box"
               :class="{'item-img-grey': Number(userMes?.countTrain) === 0 || (userInfo.id !== userMes?.user?.uid && !userMes?.openTrain),
               'item-img-red': Number(userMes?.diffTrain) > 0}">
            <div class="pt-18">
              <div>
                {{
                  userMes?.openTrain || myUser ? (Number(userMes?.countTrain) === 0 ? `-` : `${userMes?.diffTrain || 0}`) : `*`
                }}<span
                  style="font-size: 12px">%</span></div>
              <div class="diff-text">异鉴率</div>
            </div>
          </div>
          <div class="count-text pt-9">{{
              userMes?.openTrain || myUser ? (Number(userMes?.countTrain) === 0 ? `未鉴定` : `鉴定量 ${formatWan(userMes?.countTrain) || 0}`) : '未公开'
            }}
          </div>
          <div style="height: 14px"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import purpleShieldIcon from "@/assets/images/identify/purpleShieldIcon.png"
import GradeTag from "@/views/identify/grade/components/gradeTag.vue";
import {computed} from "vue";
import {getCache} from "@/utils/cache.js";
import {isMagApp} from "@/utils/common.js";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();

const userInfo = computed(() => {
  return authStore.userInfo
})


const props = defineProps({
  userMes: {
    type: Object,
    default: () => {
    }
  },
  myUser: {
    type: Boolean,
    default: false,
  }
})

const emit = defineEmits(['grade'])

//占用的百分比
const getPercentage = (first, total) => {
  const countFirstAll = Number(first) || 0;
  const countAll = Number(total) || 0;
  if (countAll === 0) {
    return '0%';
  }
  const percentage = (countFirstAll / countAll) * 100;
  return `${percentage.toFixed(0)}%`;
}


//处理数字超过1w的情况
const formatWan = (num) => {
  if (typeof num !== 'number' || isNaN(num)) {
    return '0';
  }
  if (num >= 10000) {
    return (num / 10000).toFixed(1).replace(/\.0$/, '') + 'w';
  } else {
    return num.toString();
  }
}

const handleUser = () => {
  if (isMagApp()) {
    window.mag.newWin(`magapp://userHome?userId=${props.userMes?.user?.uid}`)
  } else {
    alert('请下载中羽在线APP使用')
  }
}

const handleGrade = () => {
  emit('grade')
}


const handleToast = (type) => {
  showToast(`${type}表态数据`)
}


const userGroupList = computed(() => {
  return JSON.parse(getCache('USER_GROUP_LIST')).reduce((obj, item) => {
    obj[item.groupId] = item
    return obj
  }, {})
})
</script>

<style lang="scss" scoped>
.other-user {
  margin: 10px 12px 0 12px;

  .top-user {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .user-box {
      display: flex;
      align-items: center;
      color: #3D3D3D;
      font-size: 14px;

      .avatar-box {
        position: relative;
        margin-right: 8px;
        width: 54px;
        height: 54px;
        border-radius: 50%;

        .avatar-img {
          width: 54px;
          height: 54px;
          border-radius: 50%;
        }

        .tycoon-img {
          position: absolute;
          bottom: 0;
          right: 1px;
          width: 16px;
          height: 16px;
        }
      }

      .user-desc {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .desc-box {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 10px;
          margin-top: 3px;

          .tag {
            color: #FFFFFF;
            padding: 2px 3px;
            border-radius: 3px;
            background-color: #819FFF;
            margin-right: 5px;
          }

          .pink {
            background-color: #FF7CAA;
          }

          .purple {
            background-color: #819FFF;
          }

          .orange {
            background-color: #FF8F1F;
          }

          .golden {
            color: #3D3D3D;
            font-weight: 500;
            border: 1px solid #FFE3B9;
            background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
          }

          .green {
            background-color: #00AC72;
          }

          .train-box {
            display: flex;
            align-items: center;
            margin-left: 5px;
            border-radius: 3px;
            background-color: #F1ECFF;

            .jd-icon {
              width: 9px;
              height: 10px;
              padding: 2px 3px;
            }

            .jd-text {
              color: #9673FF;
              font-size: 10px;
              font-weight: 600;
              padding-right: 3px;
            }
          }

        }

        .desc {
          font-size: 10px;
          font-weight: 400;
          margin-top: 3px;
          color: #999999;
        }
      }
    }

    .grade-btn {
      color: #666666;
      font-size: 13px;
      padding: 7px 18px;
      border-radius: 16px;
      border: 1px solid #D8D8D8;
    }
  }


  .statistics-box {
    margin: 15px 0 5px;
    display: flex;
    gap: 12px;
  }

  .box-item {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    min-width: 0;
    text-align: center;
    color: #3D3D3D;
    background: #FFFFFF;
    box-shadow: 0 1px 5px 1px rgba(0, 0, 0, 0.05);
    border-radius: 8px;

    .item-tag {
      position: absolute;
      top: 0;
      left: 0;
      color: #FF5900;
      font-size: 10px;
      font-weight: 600;
      padding: 3px 5px;
      background-color: #FFEFDE;
      border-radius: 8px 0px 8px 0px;
    }

    .add-up-tag {
      color: #486CFF;
      background-color: #EAEEFF;
    }

    .train-tag {
      color: #9673FF;
      background-color: #F1ECFF;
    }

    .item-number {
      padding-top: 18px;
      font-size: 20px;
      font-weight: 700;
      color: #3D3D3D;

      .center-text {
        padding-top: 9px;
        font-size: 12px;
        font-weight: 400;
      }

      .bottom-text {
        padding: 5px 0 11px;
        color: #C1C1C1;
        font-size: 13px;
        font-weight: 600;
      }
    }

    .item-img-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 12px 0;
    }

    .item-img-box {
      width: 54px;
      height: 60px;
      background-image: url("@/assets/images/common/greenBg.png");
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      font-size: 14px;
      font-weight: 600;

      .diff-text {
        font-size: 9px;
        font-weight: 400;
        padding-top: 3px
      }
    }

    .item-img-red {
      background-image: url("@/assets/images/common/redBg.png");
    }

    .item-img-grey {
      background-image: url("@/assets/images/common/greyBg.png");
    }
    .first-box {
      display: flex;
      align-items: center;
      padding-top: 9px;
      font-size: 10px;
      font-weight: 400;
      .first-line {
        color: #B0B0B0;
        margin: 0 5px;
      }
    }
    .count-text {
      padding-top: 5px;
      color: #B0B0B0;
      font-size: 12px;
      font-weight: 600;
    }
  }
}
</style>