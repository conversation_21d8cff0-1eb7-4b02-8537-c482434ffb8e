<template>
  <van-popup
      v-model:show="modifyShow"
      round
      position="bottom"
  >
    <div class="precautions-popup">
      <div class="title-box">
        <div></div>
        <div>修改鉴定事项</div>
        <div></div>
      </div>
      <div class="tips mt-8 flex-col-hc pb-25">温馨提示：满足以下条件即可获得修改已有鉴定结果</div>
      <div class="content-box">
        <div v-for="(item, index) in list" :key="index">
          <div class="content-text">{{item.title}}</div>
        </div>
      </div>
      <div class="footer-btn" @click="changeKnow">
        我知道了
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref } from "vue";



const modifyShow = ref(false)
const emit = defineEmits(['close'])

const list = ref([
  {title: '①.条件一：鉴定等级需大于等于热心四级'},
  {title: '②.条件二：近7日内首鉴次数大于等于30次，且需首鉴新发72小时内的求鉴'},
  {title: '③.条件三：近7日内没有修改过'},
  {title: '④.若7日内满足条件后已有修改，需从修改时为起点再次计算下一个可修改时间（注意：再次可修改需要满足上方条件一、二）'}
])



const changeKnow = () =>{
  hide()
  emit('close')
}


const show = () => {
  modifyShow.value = true
}
const hide = () => {
  modifyShow.value = false
}


defineExpose({show, hide})

</script>

<style lang="scss" scoped>
.precautions-popup {
  margin: 15px 12px 20px;
  box-sizing: border-box;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-weight: 600;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .tips {
    color: #666666;
    font-size: 13px;
    font-weight: 400;
  }

  .content-box {
    font-size: 14px;
    color: #3D3D3D;
    .content-text {
      line-height: 1.4;
      margin-bottom: 20px;
    }
  }
  .footer-btn {
    color: #FFFFFF;
    font-size: 16px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #478E87;
  }
}
</style>