<template>
  <div v-if="((groupId >= 12 || (groupId === 3 && uid !== 117229)) && !author) || gradePage">
   <div class="tag-box">
     <template v-for="item in userIcon">
       <img class="icon-img" :src="userGrade === 'a' ? loveIcon :
      userGrade === 'b' ? jewelIcon :
      userGrade === 'c' ? goldenShield :
      userGrade === 'd' ? crownIcon : ''
      "/>
     </template>
     <template v-if="userHalf">
       <img class="icon-img" :src="userGrade === 'a' ? loveHalfIcon :
      userGrade === 'b' ? jewelHalfIcon :
      userGrade === 'c' ? goldenShieldHalf :
      userGrade === 'd' ? crownHalfIcon : ''
      "/>
     </template>
     <template v-for="item in userIconGrey">
       <img class="icon-img" :src="userGrade === 'a' ? loveGreyIcon :
      userGrade === 'b' ? loveIcon :
      userGrade === 'c' ? jewelIcon :
      userGrade === 'd' ? goldenShield : ''
      "/>
     </template>
   </div>
 </div>
  <img v-else-if="groupId < 12 && ![1,2].includes(groupId) && !author &&  uid !== 117229 && !gradePage" class="no-permission" :src="noPermission">
  <div v-if="([1,2].includes(groupId) || author || uid === 117229) && !gradePage" class="detail-mes" >
    <div v-if="groupId" class="user-tag"
         :class="[Number(groupId) === 1 ? 'orange' :
                Number(groupId) === 2 ? 'orange' :
                Number(groupId) === 40 ? 'golden' :
                Number(groupId) === 3 ? 'green' :
                Number(groupId) === 2? 'pink' : 'purple']">
      {{ userGroupList[groupId].groupName }}
    </div>
  </div>
</template>

<script setup>
import {computed, ref, watch} from "vue";
import loveIcon from "@/assets/images/common/loveIcon.png";
import jewelIcon from "@/assets/images/common/jewelIcon.png";
import goldenShield from "@/assets/images/common/goldenShield.png";
import crownIcon from "@/assets/images/common/crownIcon.png";
import loveHalfIcon from "@/assets/images/common/loveHalfIcon.png";
import jewelHalfIcon from "@/assets/images/common/jewelHalfIcon.png";
import goldenShieldHalf from "@/assets/images/common/goldenShieldHalf.png";
import crownHalfIcon from "@/assets/images/common/crownHalfIcon.png";
import loveGreyIcon from "@/assets/images/common/loveGreyIcon.png";
import jewelGreyIcon from "@/assets/images/common/jewelGreyIcon.png";
import goldenShieldGrey from "@/assets/images/common/goldenShieldGrey.png";
import crownGreyIcon from "@/assets/images/common/crownGreyIcon.png";
import noPermission from "@/assets/images/common/noPermission.png"
import {getCache} from "@/utils/cache.js";

const props = defineProps({
  userLevel: {
    type: String,
    default: ''
  },
  author: {
    type: Boolean,
    default: false
  },
  gradePage: {
    type: Boolean,
    default: false
  },
  uid: {
    type: Number,
    default: 0
  },
  groupId: {
    type: Number,
    default: 0
  }
})

const userGrade = ref()
const userIcon = ref()
const userIconGrey = ref()
const userHalf = ref()


const userGroupList = computed(() => {
  return JSON.parse(getCache('USER_GROUP_LIST')).reduce((obj, item) => {
    obj[item.groupId] = item
    return obj
  }, {})
})


watch(() => props.userLevel,
    (val) => {
      userGrade.value = val.charAt(0)
      userIcon.value = Number(val.charAt(2));
      userHalf.value = val.slice(3);
      userIconGrey.value = userHalf.value ? 5 - userIcon.value - 1 : 5 - userIcon.value
    }, {
      deep: true,
      immediate: true
    })

</script>

<style lang="scss" scoped>

.tag-box {
  display: flex;
  align-items: center;
  min-height: 20px;
}

.icon-img {
  width: 16px;
  height: 16px;
}

.no-permission {
  width: 78px;
  height: 18px;
  vertical-align: top;
}

.user-tag {
  display: flex;
  align-items: center;
  font-size: 10px;
  padding: 3px 4px;
  color: #FFFFFF;
  border-radius: 3px;
}

.author-tag {
  color: #448FFF;
  margin-left: 5px;
  padding: 2px 3px;
  border: 1px solid #448FFF;
}

.pink {
  background-color: #FF7CAA;
}

.purple {
  background-color: #819FFF;
}

.orange {
  background-color: #FF8F1F;
}

.golden {
  color: #3D3D3D;
  font-weight: 500;
  border: 1px solid #FFE3B9;
  background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
}

.green {
  background-color: #00AC72;
}

.dark-green {
  background-color: #00AC72;
}

</style>