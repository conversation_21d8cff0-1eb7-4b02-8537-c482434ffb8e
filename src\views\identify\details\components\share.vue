<template>
  <div class="share-page">
    <div class="share-item" v-for="item in platformList" :key="item.name" @click="handleShare(item)">
      <img class="image-icon" :src="item.icon"/>
      <div class="text">{{item.name}}</div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted,computed} from "vue";
import pyqIcon from "@/assets/images/details/pyqIcon.png"
import wxIcon from "@/assets/images/details/wxIcon.png"
import qqIcon from "@/assets/images/details/qqIcon.png"
import qqkjIcon from "@/assets/images/details/qqkjIcon.png"
import wbIcon from "@/assets/images/details/wbIcon.png"
import { useAuthStore } from "@/stores/auth.js";
import {isMagApp} from "@/utils/common.js";
const authStore = useAuthStore()

const props = defineProps({
  info: {
    type: Object,
    default: {}
  }
})

const userInfo = computed(() => {
   return authStore.userInfo
})

const platformList = ref([
  {name: '朋友圈', type: 'WEIXIN_CIRCLE', icon: pyqIcon},
  {name: '微信好友', type: 'WEIXIN',  icon: wxIcon},
  {name: 'QQ好友', type: 'QQ', icon: qqIcon},
  {name: 'QQ空间', type: 'QZONE',  icon: qqkjIcon},
  {name: '微博', type: 'WEIBO',  icon: wbIcon}
])

const handleShare = (item) => {
  if(isMagApp()) {
    window.mag.share(item.type, function (res) {
      // alert('success with - '+res);
      console.log('res', res)
    });
  } else {
    alert('请下载中羽在线APP使用')
  }
}

onMounted(() => {
  // let url =  `${window.location.href}&uid=${userInfo.value.id}`
  window.mag.setData({
    shareData: {
      title: `[求鉴定]${props.info.name}`,
      des: props.info.brandName + props.info.cateName,
      // picurl: props.info?.attachList ? props.info.attachList[0].path : '',
      picurl: props.info?.attachList ? `${props.info.attachList[0].path}?imageView2/1/w/200/h/200/q/75/format/webp` : '',
      linkurl: window.location.href,
      // 只是分享图片, 为0就是正常分享
      // type: 1,
      // 分享图片的链接
      // imageurl: 'https://www.hiinterface.cn/index.php/png'
    }
  });
})
</script>

<style lang="scss" scoped>
.share-page {
  margin: 20px 25px 0;
  display: flex;
  justify-content: space-between;
  .share-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .image-icon {
    width: 36px;
    height: 36px;
  }
  .text {
    margin-top: 1px;
    color: #3D3D3D;
    font-size: 11px;
    font-weight: 400;
  }
}
</style>