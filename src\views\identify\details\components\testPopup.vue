<template>
  <van-popup :show="rankingPopup" round position="bottom" @click-overlay="hide">
    <van-field v-model="value" label="文本" placeholder="请输入用户名" type="textarea" style="height: 200px"/>
  </van-popup>
</template>

<script setup>

import {ref} from "vue";


const value = ref('')
const rankingPopup = ref(false)

const open = () => {
  rankingPopup.value = true
}

const hide = () => {
  rankingPopup.value = false
}

defineExpose({open, hide,})
</script>

<style scoped>

</style>