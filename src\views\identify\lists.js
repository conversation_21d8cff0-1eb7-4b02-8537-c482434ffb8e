import {ref} from "vue";
import qpzt from "@/assets/images/identify/qpzt.png";
import txjt from "@/assets/images/identify/txjt.png";
import yyggh from "@/assets/images/identify/yyggh.png";
import yyzggh from "@/assets/images/identify/yyzggh.png";
import yyzggg from "@/assets/images/identify/yyzggg.png";
import hxd from "@/assets/images/identify/hxd.png";
import fwm from "@/assets/images/identify/fwm.png";
import bg from "@/assets/images/identify/bg.png";
import btb from "@/assets/images/identify/btb.png";
import qtgh from "@/assets/images/identify/qtgh.png";
import addIcon from "@/assets/images/identify/addIcon.png";
import xzcm from "@/assets/images/identify/xzcm.png";
import xntb from "@/assets/images/identify/xntb.png";
import xnd from "@/assets/images/identify/xnd.png";
import xg from "@/assets/images/identify/xg.png";
import xs from "@/assets/images/identify/xs.png";
import xdz from "@/assets/images/identify/xdz.png";
import xdb from "@/assets/images/identify/xdb.png";
import xhtb from "@/assets/images/identify/xhtb.png";
import qtzt from "@/assets/images/identify/qtzt.png";
import ymqzt from "@/assets/images/identify/ymqzt.png";
import qttz from "@/assets/images/identify/qttz.png";
import ymqpp from "@/assets/images/identify/ymqpp.png";
import tgzm from "@/assets/images/identify/tgzm.png";
import qgfm from "@/assets/images/identify/qgfm.png";
import fwtz from "@/assets/images/identify/fwtz.png";
import hgz from "@/assets/images/identify/hgz.png";
import txm from "@/assets/images/identify/txm.png";
import ztwg from "@/assets/images/identify/ztwg.png";
import zmbz from "@/assets/images/identify/zmbz.png"
import bmbz from "@/assets/images/identify/bmbz.png"

// 尤尼克斯羽毛球拍
export const yyBadmintonRacketList = ref([
    {
        name: '整体外观',
        icon: qpzt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: 'T型接头',
        icon: txjt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '中杆钢号',
        icon: yyggh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '锥盖钢号',
        icon: yyzggh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '锥盖规格',
        icon: yyzggg,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '防伪码',
        icon: fwm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '护线钉',
        icon: hxd,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '拍柄盖',
        icon: bg,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '拍柄贴标',
        icon: btb,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {name: '其他补充', icon: addIcon, require: false, censor: true},
]);
// 李宁羽毛球拍
export const lnBadmintonRacketList = ref([
    {
        name: '整体外观',
        icon: qpzt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: 'T型接头',
        icon: txjt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '中杆钢号',
        icon: yyggh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '锥盖规格',
        icon: yyzggh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '防伪码',
        icon: fwm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '护线钉',
        icon: hxd,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '拍柄盖',
        icon: bg,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '其他钢号',
        icon: qtgh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '拍柄贴标',
        icon: btb,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {name: '其他补充', icon: addIcon, require: false, censor: true},
]);
// 威克多 其他 -羽毛球拍
export const slBadmintonRacketList = ref([
    {
        name: '整体外观',
        icon: qpzt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: 'T型接头',
        icon: txjt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '中杆钢号',
        icon: yyggh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '锥盖规格',
        icon: yyzggh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '防伪码',
        icon: fwm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '护线钉',
        icon: hxd,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '拍柄盖',
        icon: bg,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '拍柄贴标',
        icon: btb,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {name: '其他补充', icon: addIcon, require: false, censor: true},
]);

// 运动鞋
export const sneakersList = ref([
    {
        name: '鞋子侧面',
        icon: xzcm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '鞋内贴标',
        icon: xntb,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '鞋内底',
        icon: xnd,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '鞋背LOGO',
        icon: xg,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '鞋舌图案',
        icon: xs,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '鞋垫正面',
        icon: xdz,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '鞋垫背面',
        icon: xdb,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '鞋盒贴标',
        icon: xhtb,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {name: '其他补充', icon: addIcon, require: false, censor: true},
]);

// 羽毛球
export const badmintonList = ref([
    {
        name: '整体外观',
        icon: qtzt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '羽毛球整体',
        icon: ymqzt,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '球头贴纸',
        icon: qttz,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '球桶LOGO',
        icon: ymqpp,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '桶盖正面',
        icon: tgzm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '桶盖反面',
        icon: qgfm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '防伪贴纸',
        icon: fwtz,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '合格证',
        icon: hgz,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {
        name: '条形码',
        icon: txm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: false,
        censor: true
    },
    {name: '其他补充', icon: addIcon, require: false, censor: true},
]);

//球拍线
export const racketString = ref([
    {
        name: '正面包装',
        icon: zmbz,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '背面包装',
        icon: bmbz,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '背面贴纸',
        icon: txm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '防伪码',
        icon: fwm,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {
        name: '线标特写',
        icon: yyggh,
        exampleIcon: 'https://www.badmintoncn.com/cbo_ea/img/<EMAIL>',
        require: true,
        censor: true
    },
    {name: '其他补充', icon: addIcon, require: false, censor: true},
])

// 其他物品
export const otherList = ref([
    {name: '整体外观', icon: ztwg, require: true, censor: true},
    {name: '品牌LOGO', icon: qttz, require: true, censor: true},
    {name: '条形码', icon: txm, require: false, censor: true},
    {name: '其他补充', icon: addIcon, require: false, censor: true},
]);


export const categoryBrandMap = {
    '球拍': {
        '尤尼克斯': yyBadmintonRacketList.value,
        '李宁': lnBadmintonRacketList.value,
        '其他': lnBadmintonRacketList.value,
        '威克多': slBadmintonRacketList.value
    },
    '羽毛球拍': {
        '尤尼克斯': yyBadmintonRacketList.value,
        '李宁': lnBadmintonRacketList.value,
        '其他': lnBadmintonRacketList.value,
        '威克多': slBadmintonRacketList.value
    },
    '球鞋': sneakersList.value,
    '羽毛球鞋': sneakersList.value,
    '羽毛球': badmintonList.value,
    '球拍线': racketString.value,
};


//定义品类

export const commonCategory = ref([
    {
        id: -1,
        name: "全部",
        allName: "全部"
    },
    {
        id: 1,
        name: "球拍",
        allName: "羽毛球拍"
    },
    {
        id: 3,
        name: "球鞋",
        allName: "羽毛球鞋"
    },
    {
        id: 8,
        name: "羽毛球",
        allName: "羽毛球"
    },
    {
        id: 7,
        name: "球拍线",
        allName: "球拍线"
    },
    {
        id: 0,
        name: "其他",
        allName: "其他"
    }
])