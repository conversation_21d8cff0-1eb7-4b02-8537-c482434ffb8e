<template>
  <div class="expert-page">
    <div class="header-box">
      <div class="title">{{ title }}</div>
      <div v-if="moreShow" class="flex-vc more" @click="handleRanking">
        <div class="pr-3">更多</div>
        <van-icon name="arrow" size="10"/>
      </div>
    </div>
    <div class="expert-box">
      <div class="user-box" v-for="(item, index) in list" :key="index" @click="handleUser(item)">
<!--        <img class="user-avatar" :src="item?.user?.avatar">-->
        <van-image class="user-avatar"
                   lazy-load
                   :src="item?.user?.avatar" />
        <div class="user-name">{{ item?.user?.name }}</div>
        <div class="mt-5">
          <GradeTag :group-id="item?.user?.groupId" :uid="item?.user?.uid" :userLevel="item?.user?.userLevel"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import GradeTag from "@/views/identify/grade/components/gradeTag.vue";
import {useAuthStore} from "@/stores/auth.js";
import {useRouter} from "vue-router";
import {jumpUser} from "@/utils/common.js";

const authStore = useAuthStore()
const router = useRouter()


const props = defineProps({
  title: {
    type: String,
    default: '鉴定达人'
  },
  moreShow: {
    type: Boolean,
    default: false
  },
  list: {
    type: Array,
    default: () => []
  },
  rankingType: {
    type: Number,
    default: 0
  }
})

const handleRanking = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/details/statistics?mag_hide_progress=1`, {type: props.rankingType});
  } else {
    router.push({
      name: 'statistics',
      query: {
        type: props.rankingType
      }
    })
  }

}

const handleUser = (item) => {
  jumpUser(item.user.uid)
}
</script>

<style scoped lang="scss">
.expert-page {
  margin: 0  0 12px;
  width: 100%;
  background: #F5F5F5;
  border-radius: 10px;

  .header-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;

    .title {
      color: #3D3D3D;
      font-size: 16px;
    }

    .more {
      color: #999999;
      font-weight: 400;
      font-size: 13px;
      margin-right: 3px;
    }
  }
}

.expert-box {
  padding: 0 12px 12px;
  display: flex;
  justify-content: space-between;

  .user-box {
    display: flex;
    align-items: center;
    flex-direction: column;

    ::v-deep .icon-img {
      width: 12px !important;
      height: 12px !important;
    }

    .user-avatar {
      width: 54px;
      height: 54px;
      overflow: hidden;
      border-radius: 50%;
    }

    .user-name {
      width: 70px;
      padding-top: 10px;
      color: #3D3D3D;
      font-size: 13px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

}
</style>