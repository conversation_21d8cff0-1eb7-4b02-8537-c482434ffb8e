<template>
  <div class="my-appraisal" :style="{ paddingTop: `${titleHeight}px`}">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      :style="{ height: `calc(100vh - 10px - ${titleHeight}px)`}">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="finishedText"
          @load="onLoad"
          v-model:error="error"
          :error.sync="errorStatus"
          error-text="请求失败，点击重新加载"
          :immediate-check="false"
          ref="listRef"
      >
        <!--        <div ref="commentAnchor"></div>-->
        <OtherUser :userMes="userMes" :myUser="true" @grade="handleGrade"/>
        <div class="sticky-tabs">
          <van-tabs class="tab-box"
                    v-model:active="activeTab"
                    @click-tab="tabChange"
                    :disabled="isTabLoading"
                    ref="tabsRef"
                    color="#478E87"
                    line-width="28"
                    line-height="2"
                    title-active-color="#478E87"
                    title-inactive-color="#3D3D3D">
            <van-tab :title="tab.name" :name="tab.id" v-for="(tab, index) of tabs" :key="index"
                     :disabled="isTabLoading && tab.id !== activeTab"/>
          </van-tabs>
          <div style="border: 1px solid #F5F5F5; margin-top: -1px;"></div>
        </div>
        <div v-if="activeTab !== 3" class="tags-box flex-vc relative">
          <div class="tags-page">
            <div
                v-for="(item, index) in categoryList"
                :key="index"
                class="tag-item"
                :class="{'active-item': category === item.id}"
                @click="toggleSelection(item)"
            >
              <div>{{ item.name }}</div>
              <div class="pt-1">{{ item.count }}</div>
            </div>
          </div>
          <!--          <img v-if="activeTab === 2" class="tag-filter" :src="filterIcon" @click="handleScreen"/>-->
          <img v-if="categoryList?.length" class="tag-filter" :src="filterIcon" @click="handleScreen"/>
          <div v-if="activeTab === 2 && diffNew" class="circle-box"></div>
        </div>
        <div v-if="!expireType && activeTab === 2" class="modify-box relative" @click="changeModify">
          <div>温馨提示：满足一定条件即可修改已有鉴定结果</div>
          <div class="btn-text">查看</div>
          <img class="modify-icon" :src="tipClose"/>
        </div>
        <div v-if="tableList.length">
          <div v-for="(item, index) in tableList" :key="index">
            <div class="mes-content" @click="handleDetails(item)" :style="{marginTop: activeTab === 3 ? '10px' : 0}">
              <div class="picture-box">
                <!--                <img v-if="item?.picture.length" class="img-box"-->
                <!--                     :src="`${item.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`"/>-->
                <van-image
                    v-if="item?.picture?.length && activeTab !== 3"
                    class="img-box"
                    lazy-load
                    fit="cover"
                    :src="`${item.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`"
                />
                <van-image
                    v-if="item?.pic && activeTab === 3"
                    class="img-box"
                    lazy-load
                    fit="cover"
                    :src="`${item.pic}?imageView2/1/w/200/h/200/q/75/format/webp`"
                />
                <img v-if="!item?.picture?.length && !item?.pic" class="img-box" :src="hollowIcon"/>
                <div v-if="activeTab === 2 && item.diff" class="diff-box">
                  <div class="diff-text">异鉴</div>
                </div>
                <div v-if="item.hot > 1 && item.display !== -1 && item.display !== -2 && activeTab === 1"
                     class="cover-box promotion-box">推广中
                </div>
                <div v-if="item.hot === 1 && item.display !== -1 && item.display !== -2  && activeTab === 1"
                     class="cover-box expire-box">推广到期
                </div>
                <div v-if="item.display === -1" class="cover-box">审核中</div>
                <div v-if="item.display === -2" class="cover-box">已被屏蔽</div>
                <div class="image-top" v-if="item?.real + item?.fake !== 0 && !userMes?.train && activeTab !== 3">
                  <div class="top-people" :style="{ 'background-color': getBackgroundColor(item.real, item.fake) }">
                    <img class="jd-icon" :src="shieldIcon"/>
                    <div class="pr-4">{{ item.real + item.fake }}</div>
                  </div>
                  <div class="top-ratio" v-if="item.real + item.fake >= 2"
                       :style="{ 'background-color': getBackgroundColor(item.real, item.fake) }">
                    {{ getDominantPercentage(item.real, item.fake) }}
                    <span class="per-text">%</span>
                  </div>
                </div>
                <!--                <div class="image-top" v-if="item.real + item.fake !== 0"-->
                <!--                     :style="{ 'background-color': getBackgroundColor(item.real, item.fake) }">-->
                <!--                  <img class="jd-icon" :src="shieldIcon"/>-->
                <!--                  <div>{{ item.real + item.fake }}</div>-->
                <!--                  <div class="cut-line">{{ '|' }}</div>-->
                <!--                  <div class="pr-4">{{ getDominantPercentage(item.real, item.fake) }}</div>-->
                <!--                </div>-->
              </div>
              <div v-if="activeTab !== 3" class="mes-box">
                <div>
                  <div class="title-container">
                    <div class="title-box">
                      <div v-if="item?.recommend" class="title-tag">推荐</div>
                      <div class="mes-title">{{ item.name }}</div>
                    </div>
                    <div style="width: 10px"></div>
                    <div class="reward-box" v-if="activeTab === 2">
                      <div v-if="item?.red && item?.redAmountMine > 0 && item?.redClaimMine">
                        <span style="font-size: 10px">¥</span>
                        <span>{{ `${Number(item.redAmountMine).toFixed(2)}` }}</span>
                      </div>
                      <div v-if="item?.red && item?.redAmountMine <= 0 && item?.redClaimMine">
                        <span style="font-size: 13px; color: #A8A8A8; font-weight: 500; flex-shrink: 0">未中奖</span>
                      </div>
                      <div v-if="item?.red && item?.redQ && !item?.redClaimMine && item?.redTime <= 0"
                           class="reward-btn">抽
                      </div>
                    </div>
                  </div>
                  <div class="mes-desc flex-vc"
                       :class="{'mt-4': activeTab === 2 && !(item?.red && item?.redQ && !item?.redClaimMine && item?.redTime <= 0)}">
                    <div v-if="activeTab === 2 && item.train" class="train-tag">训</div>
                    <div v-if="activeTab === 2" class="mes-tag flex-vc" :class="{'red-tag' : !item.appraisal}">
                      <img class="tag-icon" :src="shieldIcon"/>
                      <div class="mt-1">{{ !item.appraisal ? '看假' : '看正' }}</div>
                    </div>
                    <div class="mes-time" :class="{'mt-6': activeTab === 1}">
                      {{ activeTab === 1 ? `发布于${item.createTime}` : `${item.createTime}` }}
                    </div>
                  </div>
                  <div v-if="activeTab === 1 && item?.red" class="red-packet-box"
                       :class="{'red-packet-active': !item?.redClaim }">
                    <span class="red-packet" :class="{'red-packet-border': !item?.redClaim }">{{
                        `¥${Number(item?.redAmount).toFixed(2)}｜${item?.redAmount * 2}个红包`
                      }}</span>
                  </div>
                </div>
                <div v-if="activeTab === 2" class="brand-left">
                  <div class="brand-name pl-0">{{ `${item.brand} | ${item.cate}` }}</div>
                </div>
                <div v-if="activeTab === 1" class="brand-box">
                  <div class="brand-left">
                    <img class="brand-icon" :src="item?.brandLogo"/>
                    <div class="brand-name">{{ `${item.brand} | ${item.cate}` }}</div>
                  </div>
                  <div class="brand-right">
                    <img class="eye-icon" :src="eyeIcon"/>
                    <div>{{ item.read }}</div>
                  </div>
                </div>
                <div v-if="activeTab === 2" class="user-box">
                  <div class="user-left">
                    <!--                    <img class="user-avatar" :src="item?.user?.avatar"/>-->
                    <van-image
                        v-if="item?.user?.avatar"
                        class="user-avatar"
                        lazy-load
                        :src="item?.user?.avatar"
                    />
                    <div class="user-name">{{ item?.user?.name }}</div>
                  </div>
                  <div class="user-right">
                    <img class="eye-icon" :src="eyeIcon"/>
                    <div>{{ item.read }}</div>
                  </div>
                </div>
              </div>
              <div v-if="activeTab === 3" class="mes-text-box">
                <div class="item-name">{{ item.name }}</div>
                <div class="item-time">{{ `发布于${item.createTime}` }}</div>
              </div>
            </div>
            <div style="height: 1px; background: #F5F5F5; margin: 12px"></div>
          </div>
        </div>
        <div>
          <Loading ref="subLoadingRef" discText="加载中..." class="loading-box"/>
        </div>
        <div v-if="!tableList.length && !loading">
          <Empty class="empty-wrap"/>
        </div>
      </van-list>
    </van-pull-refresh>
    <!--    <Tabbar :page="4"/>-->
    <AttributePopup ref="attributePopupRef" @save="handleSave"/>
    <ModifyPopup ref="modifyPopupRef"/>
    <ScreenPopup ref="ScreenPopupRef" :userMes="userMes" :diffNew="diffNew" :rewardShow="true"
                 @confirm="handleConfirm"/>
    <div class="posted-btn" @click="handleSetUp">
      <div class="posted-box">
        <img :src="myBtn"/>
      </div>
    </div>
  </div>

</template>

<script setup>
import {computed, ref, onMounted, nextTick} from "vue";
import {formatDate, isUserGroup, jumpLogin, otherLogin} from "@/utils/common"
import OtherUser from "@/views/identify/heAppraisal/components/otherUser.vue";
import AttributePopup from "@/views/identify/components/attributePopup.vue";
import Loading from "@/components/loading/index.vue"
import Tabbar from "@/components/tabbar.vue";
import ScreenPopup from "@/views/identify/myAppraisal/components/screenPopup.vue";
import ModifyPopup from "@/views/identify/components/modifyPopup.vue";
import filterIcon from "@/assets/images/identify/filterIcon.png"
import tipClose from "@/assets/images/common/tipClose.png"
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import hollowIcon from "@/assets/images/common/hollowIcon.png"
import eyeIcon from "@/assets/images/common/eyeIcon.png"
import myBtn from "@/assets/images/common/myBtn.svg"
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
import {debounce} from "lodash";
import router from "@/router/index.js";
import {setCache, getCache, getCookie} from "@/utils/cache.js";

const authStore = useAuthStore();


const userInfo = computed(() => {
  return authStore.userInfo
})

const commentAnchor = ref(null)
const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const attributePopupRef = ref('')
const modifyPopupRef = ref('')
const ScreenPopupRef = ref('')
const classify = ref('')
const refreshing = ref(false)
const loading = ref(false)
const subLoadingRef = ref()
const finished = ref(false)
const listRef = ref()
const tableList = ref([]);
const expireType = ref(true);
const finishedText = ref('')
const error = ref(false)
const errorStatus = ref(false)
const activeTab = ref()
const category = ref(-1)
const tabHeight = ref(44)
const brand = ref()
const appraisal = ref()
const diff = ref()
const red = ref(-1)
const diffNew = ref(false)  //是否有新的异鉴
const userMes = ref()
const isTabLoading = ref(false)
const params = ref({
  page: 1,
  pageSize: 10,
})
const tabs = ref([
  {
    name: "求鉴",
    id: 1,
  }, {
    name: "鉴定",
    id: 2,
  }, {
    name: "好文",
    id: 3,
  },
])

const categoryList = ref([
  {
    "id": -1,
    "count": 0,
    "name": "全部",
  },
  {
    "id": 1,
    "count": 0,
    "name": "球拍",
  },
  {
    "id": 3,
    "count": 0,
    "name": "球鞋",
  },
  {
    "id": 8,
    "count": 0,
    "name": "羽毛球",
  },
  {
    "id": 7,
    "count": 0,
    "name": "球拍线",
  },
  {
    "id": 0,
    "count": 0,
    "name": "其他",
  }
]);


const handleScreen = () => {
  ScreenPopupRef.value.show(activeTab.value)
}

// 获取背景颜色
const getBackgroundColor = (real, fake) => {
  if (real + fake === 0) return '';
  if (real > fake) {
    return '#00B578';
  } else if (fake > real) {
    return '#FA5151';
  } else {
    return '#676f6e';
  }
};

// 获取主导方的百分比
const getDominantPercentage = (real, fake) => {
  const total = real + fake;
  if (total === 0) return ''; // 如果总和为 0，返回空字符串
  if (real > fake) {
    return `${Math.round((real / total) * 100)}`; // 看正的比率
  } else if (fake > real) {
    return `${Math.round((fake / total) * 100)}`; // 看假的比率
  } else {
    return '50'; // 如果相等，返回空字符串
  }
};

const handleDetails = (item) => {
  if (item.display === -2 && (![1, 2].includes(userInfo.value.groupId)) || Number(userInfo.value.id) === 117229) {
    showToast('此内容被屏蔽或正在审核中')
    return
  }
  if (activeTab.value === 2 && item.display === -1 && ![1, 2].includes(userInfo.value.groupId) || Number(userInfo.value.id) === 117229) {
    showToast('此内容被屏蔽或正在审核中')
    return
  }
  if (activeTab.value === 3) {
    if (!authStore.phone) {
      window.location.href = `https://m2.badmintoncn.com/mag/circle/v1/forum/threadWapPage?tid=${item.tid}&themecolor=478e87&circle_id=114`;
    } else {
      window.mag.newWin(`https://m2.badmintoncn.com/mag/circle/v1/forum/threadWapPage?&mag_hide_progress=1&tid=${item.tid}&themecolor=478e87&circle_id=114`)
    }
  } else {
    if (authStore.phone) {
      const apiUrl = authStore.apiUrl
      window.mag.newWin(`${apiUrl}/details/index?mag_hide_progress=1&id=${item.tid}&partition=${item.partition}`);
      pageLoad()
    } else {
      router.push({
        name: 'details',
        query: {
          id: item.tid,
          partition: item.partition
        }
      })
    }
  }
}

//获取当前日期
const getCurrentTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  return `${year}-${month}-${day}`
}


const modifyClose = () => {
  setCache('EXPIRE_TIME', getCurrentTime())
  expireType.value = getCache('EXPIRE_TIME') === getCurrentTime()
}


const changeModify = () => {
  modifyPopupRef.value.show()
}

const handleGrade = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/grade/index?mag_hide_progress=1`);
    pageLoad()
  } else {
    router.push({
      name: 'grade',
    })
  }
}

const handleSetUp = () => {
  attributePopupRef.value.show()
}


const rollView = () => {
  nextTick(() => {
    if (commentAnchor.value) {
      commentAnchor.value.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start'      // 对齐到顶部
      });
    }
  });
}


const tabChange = async (item) => {
  // rollView()
  // if (isTabLoading.value) return
  // isTabLoading.value = true
  try {
    tableList.value = []
    activeTab.value = item.name
    setCache('TAB_TAG', item.name);
    category.value = -1
    diff.value = -1
    appraisal.value = -1
    red.value = -1
    brand.value = ''
    params.value.page = 1
    finished.value = false
    loading.value = false
    ScreenPopupRef.value.reset()
    // await Promise.all([getCateCount(), getPageList()])
    if (item.name == 3) {
      await getPageList()
    } else if (item.name == 1) {
      await Promise.all([getCateCount(), getPageList()])
    } else {
      await Promise.all([getCateCount(), getDiffCount(), getPageList()])
    }
  } finally {
    isTabLoading.value = false
  }
}

// const debouncedTabChange = debounce((item) => {
//   // 在防抖执行时，再次确保页码为1
//   params.value.page = 1
//   tabChange(item)
// }, 300)

const toggleSelection = (item) => {
  subLoadingRef.value.show()
  category.value = item.id
  params.value.page = 1;
  tableList.value = []
  getPageList()
  getCateCount()
  if (activeTab.value == 2) {
    getDiffCount()
  }
}


const onRefresh = () => {
  params.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    getSetting()
    getCateCount()
    getPageList();
    if (activeTab.value == 2) {
      getDiffCount()
    }
  })
}

const onLoad = () => {
  if (tableList.value) {
    params.value.page++
    nextTick(() => {
      getPageList();
    })
  }
};

const handleConfirm = (obj) => {
  brand.value = obj.brand
  appraisal.value = obj.appraisal
  red.value = obj.reward
  diff.value = activeTab.value === 2 ? obj.diff : -1
  tableList.value = []
  params.value.page = 1
  getPageList()
  getCateCount()
  if (activeTab.value == 2) {
    getDiffCount()
  }
}


const handleSave = () => {
  setTimeout(() => {
    userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
    location.reload();
  }, 300)
}

let abortController = null

// 修改后的 getPageList 函数
const getPageList = async () => {
  if (abortController) {
    abortController.abort();
  }
  abortController = new AbortController();
  try {
    let res;
    if (activeTab.value === 3) {
      const paramsCopy = {
        ...params.value,
        uid: userInfo.value && userInfo.value.id ? userInfo.value.id : null,
        // uid: 117229,
      };
      loading.value = true;
      res = await identifyApi.getMySelected(paramsCopy, {
        signal: abortController.signal,
      });
    } else {
      const paramsCopy = {
        ...params.value,
        uid: userInfo.value && userInfo.value.id ? userInfo.value.id : null,
        type: activeTab.value,
        cate: category.value === -1 ? null : category.value,
        brand: brand.value === -1 ? null : brand.value,
        red: red.value === -1 ? null : red.value === 1,
        appraisal: appraisal.value === -1 ? null : appraisal.value,
        diff: diff.value === -1 ? null : diff.value,
      };
      loading.value = true;
      res = await identifyApi.getMyEaList(paramsCopy, {
        signal: abortController.signal,
      });
    }
    if (res.code === 200) {
      const data = res.data || {};
      const dataList = data.list || [];
      if (refreshing.value) {
        tableList.value = [];
        refreshing.value = false;
      }
      tableList.value = [...tableList.value, ...dataList];
      if (dataList.length < params.value.pageSize) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = tableList.value.length ? "没有更多了" : "";
    } else {
      errorStatus.value = true;
      error.value = true;
    }
    loading.value = false
  } catch (error) {
    if (error.name !== 'AbortError') {
      errorStatus.value = true;
    }
    error.value = true;
  } finally {
    loading.value = false;
    subLoadingRef.value.hide()
    abortController = null;
  }
};

const getCateCount = async () => {
  if (activeTab.value == 3) return
  let res = await identifyApi.getThreadCount({
    uid: userInfo.value.id,
    type: activeTab.value,
    target: 'cate',
    brand: brand.value === -1 ? '' : brand.value,
    red: red.value === -1 ? null : red.value === 1,
    appraisal: appraisal.value === -1 ? null : appraisal.value,
    diff: diff.value === -1 ? null : diff.value,
  })
  if (res.code === 200) {
    categoryList.value = res.data.list
    setCache('CATEGORY_LIST', JSON.stringify(categoryList.value))
  }
}

const getDiffCount = async () => {
  if (activeTab.value == 3) return
  let res = await identifyApi.getThreadCount({
    uid: userInfo.value.id,
    type: activeTab.value,
    target: 'others',
    brand: brand.value === -1 ? '' : brand.value,
    appraisal: appraisal.value === -1 ? null : appraisal.value,
    diff: diff.value === -1 ? null : diff.value,
  })
  if (res.code === 200) {
    if (res?.data?.list?.length) {
      diffNew.value = res.data.list[1]?.notice
    }
  }
}

const resetData = async () => {
  tableList.value = []
  await getPageList()
  await getCateCount()
  await getDiffCount()
  if (authStore.phone) {
    window.mag.setTitle('我的鉴定');
  } else {
    document.title = '我的鉴定'
  }
  window.mag.showNavigation()
  const apiUrl = authStore.apiUrl
  let url = `${apiUrl}/heAppraisal/index?mag_hide_progress=1&userId=${userInfo.value.id}`
  window.mag.setData({
    shareData: {
      title: `${userMes.value?.user?.name}的鉴定数据`,
      des: `近期鉴定量${userMes.value?.count}、总计鉴定量${userMes.value?.countAll}`,
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: url,
    }
  });
}

const pageLoad = () => {
  window.mag.setPageLife({
    pageAppear: async function () {
      // activeTab.value = 1
      await resetData()
    }
  });
}

const getSetting = async () => {
  let res = await identifyApi.queryUserList({
    ids: userInfo.value.id || ''
  })
  if (res.code === 200) {
    userMes.value = res.data.list[0]
  }
}

onMounted(async () => {
  // if (getCookie('cbo_magapp_token')?.length > 1) {
  //   await otherLogin()
  // }
  await otherLogin()
  if (!authStore.token) {
    jumpLogin()
    return
  }
  if (userInfo.value.id) {
    await getSetting()
  }
  // userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
  expireType.value = getCache('EXPIRE_TIME') === getCurrentTime()
  if (getCache('TAB_TAG')) {
    activeTab.value = Number(getCache('TAB_TAG'))
  }
  nextTick(() => {
    let stickyTabsElement = document.querySelector('.sticky-tabs');
    tabHeight.value = stickyTabsElement.offsetHeight;
  });
  // window.mag.hideMore()
  subLoadingRef.value.show()
  // if (!authStore.token) {
  //   await otherLogin()
  // }
  await resetData()
  await isUserGroup()
})

</script>

<style scoped lang="scss">
.my-appraisal {
  position: relative;
  //height: 100vh;
  height: 100%;
  width: 100vw;
  background-color: #FFFFFF;

  .sticky-tabs {
    //position: fixed;
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }

  .modify-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #FF853E;
    font-size: 11px;
    font-weight: 400;
    margin: 0 12px 10px;
    padding: 7px 30px 7px 12px;
    border-radius: 5px;
    background-color: #FFF4EE;

    .btn-text {
      padding: 3px 10px;
      font-size: 13px;
      font-weight: 600;
      border-radius: 15px;
      border: 1px solid #FF853E;
    }

    .modify-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 18px;
      height: 18px;
    }
  }

  .tags-page {
    flex: 1;
    margin: 10px 12px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    column-gap: 9px;

    .tag-item {
      width: calc((100% - 45px) / 5);
      color: #3D3D3D;
      font-size: 10px;
      font-weight: 400;
      text-align: center;
      padding: 5px 0;
      border-radius: 5px;
      background-color: #F5F5F5;
    }

    .active-item {
      color: #478E87;
      font-weight: 500;
      background-color: #DFF7F6;
    }
  }

  .tag-filter {
    width: 18px;
    height: 14px;
    margin-right: 12px;
  }

  .circle-box {
    position: absolute;
    top: 8px;
    right: 12px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #FF3700;
  }

  .content-table-list {
    overflow: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .content-table-list {
    background-color: #FFFFFF;

    .mes-content {
      display: flex;
      align-items: center;
      margin: 0 12px;
      height: 90px;

      .picture-box {
        position: relative;
        width: 90px;
        height: 100%;
        flex-shrink: 0;
        margin-right: 10px;

        .img-box {
          flex-shrink: 0;
          margin-right: 10px;
          width: 90px;
          height: 90px;
          border-radius: 5px;
          overflow: hidden;
          object-fit: cover;
        }

        .diff-box {
          position: absolute;
          top: 0;
          color: #FFFFFF;
          font-size: 13px;
          text-align: center;
          border-radius: 5px 0 5px 0;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          background: rgba(250, 81, 81, 0.6);

          .diff-text {
            padding: 4px;
          }
        }

        .cover-box {
          position: absolute;
          bottom: 0;
          color: #FFFFFF;
          font-size: 13px;
          width: 100%;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-bottom-left-radius: 5px;
          border-bottom-right-radius: 5px;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          background: rgba(61, 61, 61, 0.4);
        }

        .promotion-box {
          background: rgba(71, 142, 135, 0.5);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);

        }

        .expire-box {
          background: rgba(250, 81, 81, 0.5);
          backdrop-filter: blur(50px);
          -webkit-backdrop-filter: blur(10px);

        }
      }

      .mes-box {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;


        .title-container {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .title-box {
            display: flex;
            align-items: center;
          }

          .reward-box {
            font-size: 13px;
            color: #FF3700;
            flex-shrink: 0;

            .reward-btn {
              font-size: 13px;
              color: #FF3700;
              font-weight: 600;
              //padding: 8px 13px;
              padding: 6px 13px;
              border-radius: 14px;
              border: 1px solid #FF3700;
            }
          }
        }

        .mes-title {
          color: #3D3D3D;
          font-size: 15px;
          font-weight: 400;
          line-height: 1.3;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }

        .mes-desc {
          //margin-top: 6px;

          .train-tag {
            color: #FFFFFF;
            font-size: 10px;
            padding: 3px;
            border-radius: 5px;
            background: #9673FF;
            margin-right: 3px;
          }

          .mes-tag {
            color: #FFFFFF;
            font-size: 10px;
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2px 0;
            width: 37px;
            margin-right: 4px;
            border-radius: 3px;
            background-color: #00B578;

            .tag-icon {
              width: 9px;
              height: 10px;
              margin-right: 2px;
            }
          }

          .red-tag {
            background-color: #FA5151;
          }

          .mes-time {
            color: #999999;
            font-size: 11px;
            font-weight: 400;
          }
        }

        .red-packet-box {
          margin-top: 10px;
          color: #A8A8A8;
          font-size: 11px;

          .red-packet {
            padding: 4px 4px 3px;
            border-radius: 5px;
            position: relative;
          }

          .red-packet::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 200%;
            height: 200%;
            border: 1px solid #A8A8A8;
            border-radius: 10px; // 记得是原来的两倍
            transform: scale(0.5);
            transform-origin: 0 0;
            pointer-events: none;
            box-sizing: border-box;
          }

          .red-packet-border {
            padding: 4px 4px 3px;
            border-radius: 5px;
            position: relative;
          }

          .red-packet-border::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 200%;
            height: 200%;
            border: 1px solid #FF853E !important;
            border-radius: 10px; // 记得是原来的两倍
            transform: scale(0.5);
            transform-origin: 0 0;
            pointer-events: none;
            box-sizing: border-box;
          }
        }

        .red-packet-active {
          color: #FF853E;
        }

        .brand-box {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .brand-left {
          display: flex;
          align-items: center;

          .brand-icon {
            width: 40px;
            height: 18px;
            border-radius: 3px;
          }

          .brand-name {
            color: #666666;
            font-size: 10px;
            font-weight: 400;
            padding-left: 5px;
          }
        }

        .brand-right {
          display: flex;
          align-items: center;
          color: #999999;
          font-size: 10px;
          font-weight: 400;

          .eye-icon {
            width: 16px;
            height: 16px;
            margin-right: 2px;
          }
        }

        .user-box {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .user-left {
            display: flex;
            align-items: center;

            .user-avatar {
              width: 24px;
              height: 24px;
              overflow: hidden;
              border-radius: 50%;
            }

            .user-name {
              color: #999999;
              font-size: 13px;
              font-weight: 400;
              padding-left: 5px;
            }
          }

          .user-right {
            display: flex;
            align-items: center;
            color: #999999;
            font-size: 10px;
            font-weight: 400;

            .eye-icon {
              width: 16px;
              height: 16px;
              margin-right: 2px;
            }
          }
        }
      }

      .mes-text-box {
        height: 98%;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .item-name {
          color: #3D3D3D;
          font-size: 15px;
          font-weight: 400;
          line-height: 1.3;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .item-time {
          color: #999999;
          font-size: 11px;
          font-weight: 400;
        }
      }
    }
  }

  .posted-btn {
    position: fixed;
    right: 12px;
    top: 75%;
    z-index: 100;

    .posted-box {
      width: 40px;
      height: 40px;

      .posted-img {
        width: 28px;
        height: 28px;
      }
    }
  }
}

.empty-wrap {
  padding-top: 40%
}

::v-deep .van-tab--disabled {
  color: #c8c9cc !important;
}
</style>