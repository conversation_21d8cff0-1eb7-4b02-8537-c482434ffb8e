<template>
  <div class="brand-page" :style="{ paddingTop: `${titleHeight}px`}">
    <div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                        :style="{ height: `calc(100vh - 10px - ${titleHeight}px)`}" @scroll="handleScroll">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad"
            ref="listRef"
            :immediate-check="false"
            :error.sync="errorStatus"
            @touchmove="onTouchMove"
            @touchend="onTouchEnd"
        >
          <div v-if="!searchText" class="header-container">
            <van-search v-model="searchText" readonly placeholder="搜索求鉴关键词…" shape="round"
                        @click-input="handleFilter">
              <template #left-icon>
                <img class="search-icon" :src="searchIcon">
              </template>
              <template #right-icon>
                <img class="search-filter" :src="filterIcon" @click="handleFilter">
              </template>
            </van-search>
          </div>
          <div v-if="searchText" class="search-box">
            <span style="color: #A8A8A8">搜索词：</span>
            <span>{{ searchText }}</span>
          </div>
          <!--          <div class="sticky-tabs">-->
          <!--            <Tabs :category="categoryKey" @change="handleTab"/>-->
          <!--          </div>-->
          <van-sticky :offset-top="`${titleHeight}`">
            <Tabs :category="categoryKey" @change="handleTab"/>
          </van-sticky>
          <Tags :brand="brandKey" @change="handleTag"/>
          <!--          <IdentifyList v-if="brand" ref="listRef" list-type="brand" :brand="brand"  :category="category" :searchText="searchText"/>-->
          <IdentifyList ref="IdentifyListRef" list-type="brand" :state="stateKey" :order="order" :tableList="tableList"
                        @change="handleScreen" @sort="handleSort"/>
          <div v-if="loading !== true && !tableList.length">
            <Empty class="empty-wrap"/>
          </div>
          <ReleasePopup ref="releasePopupRef" @save="handleNextStep"/>
          <MustSeePopup ref="mustSeePopupRef" @close="closeMustPopup"/>
        </van-list>
      </van-pull-refresh>
      <div class="posted" v-if="btnType !== 1">
        <div v-if="allBtn" class="all-btn" @click="handleRelease">
          <img class="edit-icon" :src="editIcon"/>
          <text>求鉴</text>
        </div>
        <div v-else key="half-btn" class="half-btn">
          <img class="edit-icon" :src="editIcon"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed, nextTick} from "vue";
import {useRouter} from "vue-router";
import {useRoute} from "vue-router";
import {getCache, getCookie, setCache} from "@/utils/cache.js";
import {getQueryParam, isUserGroup, jumpUser, otherLogin} from "@/utils/common.js";
import Tabs from "@/views/identify/components/tabs.vue";
import Tags from "@/views/identify/components/tags.vue";
import IdentifyList from "@/views/identify/components/identifyList.vue";
import ReleasePopup from "@/views/identify/components/releasePopup.vue";
import MustSeePopup from "@/views/identify/components/mustSeePopup.vue";
import searchIcon from "@/assets/images/identify/searchIcon.png"
import filterIcon from "@/assets/images/identify/filterIcon.png"
import {useAuthStore} from "@/stores/auth.js";
import identifyApi from "@/services/identify.js";
import editIcon from "@/assets/images/common/editIcon.png"

const authStore = useAuthStore();
const router = useRouter()
const route = useRoute()

const userInfo = computed(() => {
  return authStore.userInfo
})


const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const releasePopupRef = ref()
const mustSeePopupRef = ref()
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const finishedText = ref('')
const errorStatus = ref(false)
const params = ref({
  // uid: userInfo?.value && userInfo.value.id ? userInfo.value.id : null,
  page: 1,
  pageSize: 10,
})
// const order = ref(1)
const order = ref(route.query?.sort ? Number(route.query?.sort) : 1)
const red = ref(null)
// const appraisal = ref(0)
const listRef = ref()
const tableList = ref([])
const searchText = ref('')
// decodeURIComponent
// const brand = ref()
// const category = ref('')
const categoryKey = ref(route.query?.category ? Number(route.query?.category) : -1)
const stateKey = ref(0)
const btnType = ref(1)
const allBtn = ref(true)
const brandKey = ref()
const brandName = ref()
const brandLogo = ref()
const flag = ref(false)

const handleFilter = () => {
  flag.value = true
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/identify/screen?mag_hide_progress=1&backFlag=true`, {
      searchText: searchText.value,
      brandKey: brandKey.value,
      stateKey: stateKey.value,
      categoryKey: categoryKey.value
    });
  } else {
    router.push({
      name: 'screen',
      query: {
        backFlag: true,
        searchText: searchText.value,
        brandKey: brandKey.value,
        stateKey: stateKey.value,
        categoryKey: categoryKey.value
      }
    })
  }
}

const closeMustPopup = () => {
  let obj = {
    id: Number(brandKey.value),
    name: brandName.value,
    logo: brandLogo.value
  }
  releasePopupRef.value.show(obj)
}

const getSetting = async () => {
  let res = await identifyApi.getSetting({uid: userInfo.value.id})
  if (res.code === 200) {
    return res.data.list
  }
}


const handleRelease = async () => {
  if (authStore.phone) {
    if (authStore.token) {
      let res = await getSetting()
      if (res.countThread > 3) {
        closeMustPopup()
      } else {
        mustSeePopupRef.value.show()
      }
    } else {
      jumpUser()
    }
  } else {
    alert('请到中羽在线APP中发布')
  }

}


const handleNextStep = (item) => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/release/index?mag_hide_progress=1&needlogin=1`, {
      brandId: item.brandId,
      categoryId: item.categoryId,
      logo: item.logo
    })
  } else {
    router.push({
      name: 'release',
      query: {
        brandId: item.brandId,
        categoryId: item.categoryId,
        logo: item.logo
      }
    })
  }
}

const onTouchMove = (event) => {
  allBtn.value = false
}

const onTouchEnd = () => {
  allBtn.value = true
}

let scrollTimeout; // 用于检测滚动是否停止
const handleScroll = (scroll) => {
  clearTimeout(scrollTimeout);
  allBtn.value = false; // 滚动时设置为 false
  scrollTimeout = setTimeout(() => {
    allBtn.value = true; // 停止滚动后设置为 true
  }, 200); // 短暂延迟以确保滚动完全停止
}

const handleTag = (item) => {
  console.log('item', item)
  brandKey.value = item.id
  brandName.value = item.name
  brandLogo.value = item.logo
  tableList.value = [];
  params.value.page = 1;
  getSearchList()
  if (authStore.phone) {
    window.mag.setTitle(item.name);
  } else {
    document.title = item.name
  }
}
const handleTab = (item) => {
  categoryKey.value = item.name
  tableList.value = [];
  params.value.page = 1;
  getSearchList()
}

const onRefresh = () => {
  params.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    getSearchList();
  })
}

const onLoad = () => {
  if (tableList.value) {
    params.value.page++
    nextTick(() => {
      getSearchList();
    })
  }
};

const handleScreen = (val) => {
  stateKey.value = val
  params.value.page = 1;
  tableList.value = [];
  getSearchList()
}

const handleSort = (val) => {
  if (val === 4) {
    order.value = 1
    red.value = true
  } else {
    red.value = null
    order.value = val
  }
  params.value.page = 1;
  tableList.value = [];
  getSearchList()
}

const getSearchList = () => {
  let paramsCopy = {
    ...params.value,
    keyword: searchText.value,
    brand: brandKey.value === -1 ? null : brandKey.value,
    cate: categoryKey.value === -1 ? null : categoryKey.value,
    // appraisal: stateKey.value === 0 ? null : stateKey.value === 2,
    appraisal: stateKey.value === 0 ? null : stateKey.value,
    order: order.value,
    red: red.value ? red.value : null,
  };
  // return;
  loading.value = true
  identifyApi.getSearch(paramsCopy).then((res) => {
    if (res.code === 200) {
      if (refreshing.value) {
        tableList.value = [];
        refreshing.value = false;
        finished.value = false;
      }
      let data = res.data || {};
      let dataList = data.list || [];
      let records = dataList || [];
      tableList.value = tableList.value.concat(records);
      let total = data.total || 0;
      if (total <= tableList.value.length) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = "没有更多了";
      if (tableList.value.length === 0) {
        finishedText.value = "";
      }
    } else {
      errorStatus.value = true;
    }
  }).finally(() => {
    loading.value = false
  })
}

const tagList = ref([]);


const showList = () => {
  let brand = JSON.parse(getCache('BRAND_LIST'))
  tagList.value = brand
}

const getNameById = (id, list = tagList.value) => {
  const item = list.find(item => item.id === id);
  return item ? item.name : null;
};

onMounted(() => {
  showList()
  // searchText.value = route.query.searchText || getQueryParam('searchText')
  searchText.value = getCache('SEARCH_TEXT') || (route.query?.searchText ? decodeURIComponent(route.query.searchText) : '')
  brandKey.value = Number(route.query.brandKey) || Number(getQueryParam('brandKey')) || -1
  brandName.value = getNameById(Number(brandKey.value)) || '全部'
  brandLogo.value = route.query.brandLogo || getQueryParam('brandLogo')
  categoryKey.value = Number(route.query.categoryKey) || Number(getQueryParam('categoryKey')) || -1
  stateKey.value = Number(route.query.stateKey) || Number(getQueryParam('stateKey')) || 0
  btnType.value = Number(route.query.btnType) || Number(getQueryParam('btnType')) || 0
  getSearchList()
  // if(!authStore.token) {
  //   otherLogin()
  // }
  if (authStore.phone) {
    if (getCookie('cbo_magapp_token')?.length > 1) {
      otherLogin()
    }
  }
  window.mag.showNavigation()
  window.mag.setData({
    shareData: {
      title: `${brandName.value}` || '全部',
      des: '羽毛球器材鉴定，羽毛球装备鉴定，真假鉴别',
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: window.location.href,
    }
  });

  if (authStore.phone) {
    searchText.value ? window.mag.setTitle('搜索结果') : window.mag.setTitle(brandName.value)
  } else {
    searchText.value ? document.title = '搜索结果' : document.title = '品牌主页'
  }
  isUserGroup()
  // window.mag.addRefreshComponent()
})

onUnmounted(() => {
  setCache("SEARCH_TEXT", null)
  if (flag.value) {
    authStore.firstFlag = true
  }
})
window.mag.setPageLife({
  pageDisappear: function () {
    setCache("SEARCH_TEXT", null)
  }
});
</script>

<style scoped lang="scss">
.brand-page {
  width: 100vw;
  height: 100%;
  overflow: hidden;
  background-color: #FFFFFF;

  .header-container {
    display: flex;
    align-items: center;
    padding: 10px 12px 12px;

    .van-search {
      width: 100%;
      padding: 0;
      --van-search-input-height: 36px
    }

    .search-icon {
      margin-top: 6px;
      height: 14px;
      width: 14px;
    }

    .search-filter {
      margin-top: 7px;
      width: 14px;
      height: 12px;
      margin-right: 3px;
    }
  }

  .search-box {
    padding: 10px 12px 12px;
    color: #3D3D3D;
    font-size: 17px;
  }

  .content-table-list {
    overflow: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .sticky-tabs {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
  }

  .posted {
    position: absolute;
    right: 0;
    top: 75%;
    z-index: 1;

    .edit-icon {
      width: 20px;
      height: 20px;
      margin-right: 2px;
    }

    .all-btn {
      width: 76px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      font-size: 14px;
      margin-right: 12px;
      background-color: #000000;
      border-radius: 30px;
    }

    .half-btn {
      width: 44px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      font-size: 14px;
      background-color: #000000;
      border-radius: 20px 0 0 20px;
    }
  }
}

.empty-wrap {
  padding-top: 40%;
}
</style>