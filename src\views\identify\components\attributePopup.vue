<template>
  <van-popup
      v-model:show="attributeShow"
      round
      position="bottom"
      @close="handleClose"
  >
    <div class="attribute-popup">
      <div class="title-box">
        <div></div>
        <div>我的属性</div>
        <img class="close-icon" :src="closeIcon" @click="hide"/>
      </div>
      <div class="tips mt-8 flex-col-hc">温馨提示：设置后能更精准的为您推荐精彩内容</div>
      <div class="classify" >
        <TitleTop :right="false">
          <template #title>
            <div>鉴定训练</div>
          </template>
        </TitleTop>
      </div>
      <div class="study-container">
        <div>注意：开启后鉴定数据不参与正式统计</div>
        <div>
          <van-switch v-model="train" @update:model-value="onUpdateValue" active-color="#478E87"
                      inactive-color="#D8D8D8" size="22px"/>
        </div>
      </div>
      <van-divider class="line-divider" :style="{borderColor: 'f5f5f5'}"/>
      <div class="study-container">
        <div>是否向他人展示训练数据</div>
        <div>
          <van-switch v-model="openTrain" active-color="#478E87" inactive-color="#D8D8D8" size="22px"/>
        </div>
      </div>
      <div class="classify pt-25" >
        <TitleTop :right="false">
          <template #title>
            <div>擅长品牌（可多选）</div>
          </template>
        </TitleTop>
      </div>
      <div class="tag-container">
        <template v-for="(item, index) in tagList" :key="index">
          <div class="tag-item" :class="{'active-item' : activeTag.includes(item.id)}" @click="handleTag(item)">
            <span>{{ item.name }}</span>
            <div v-if="activeTag.includes(item.id)" class="check-icon">
              <img class="check-img" :src="answerIcon" />
            </div>
          </div>
        </template>
      </div>
      <div class="classify pt-30" >
        <TitleTop :right="false">
          <template #title>
            <div>擅长品类（可多选）</div>
          </template>
        </TitleTop>
      </div>
      <div class="tag-container">
        <template v-for="(item, index) in categoryList" :key="index">
          <div class="tag-item" :class="{'active-item' : activeCategory.includes(item.id)}"
               @click="handleCategory(item)">
            <span>{{ item.name }}</span>
            <div v-if="activeCategory.includes(item.id)" class="check-icon">
              <img class="check-img" :src="answerIcon" />
            </div>
          </div>
        </template>
      </div>
      <div class="footer-box">
        <div class="btn left-btn" @click="handleCancel">重置</div>
        <div class="btn right-btn" @click="handleSave">保存</div>
      </div>
    </div>
  </van-popup>
  <PrecautionsPopup ref="precautionsPopupRef"/>
</template>

<script setup>
import {ref, computed} from "vue";
import PrecautionsPopup from "@/views/identify/components/precautionsPopup.vue";
import TitleTop from "@/components/titleTop.vue";
import closeIcon from '@/assets/images/common/closeIcon.png'
import answerIcon from "@/assets/images/common/answerIcon.png"
import checkedIcon from '@/assets/images/common/checkedIcon.png'
import unCheckIcon from '@/assets/images/common/unCheckIcon.png'
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
import {setCache} from "@/utils/cache.js";
import {jumpUser} from "@/utils/common.js";
import { commonCategory } from "@/views/identify/lists.js";

const authStore = useAuthStore();


const userInfo = computed(() => {
  return authStore.userInfo
})


const attributeShow = ref(false)
const train = ref(false)
const openTrain = ref(true)
const activeTag = ref([])
const activeCategory = ref([])
const precautionsPopupRef = ref()

const tagList = ref([
  // {name: '全部', id: -1},
  {name: '尤尼克斯', id: 1},
  {name: '李宁', id: 22},
  {name: '威克多', id: 2,},
  {name: '其他', id: 0},
])

const categoryList = commonCategory.value.slice(1)

const emit = defineEmits(['save'])

const onUpdateValue = (newValue) => {
  if (newValue) {
    precautionsPopupRef.value.show()
  }
}

const handleTag = (item) => {
  if (activeTag.value.includes(item.id)) {
    activeTag.value = activeTag.value.filter(val => val !== item.id)
  } else {
    activeTag.value.push(item.id)
  }
}

const handleCategory = (item) => {
  if (activeCategory.value.includes(item.id)) {
    activeCategory.value = activeCategory.value.filter(val => val !== item.id)
  } else {
    activeCategory.value.push(item.id)
  }
}

const getUserMes = async () => {
  let res = await identifyApi.queryUserList({ids: userInfo.value.id})
  if (res.code === 200) {
    let list = res.data?.list[0]
    setCache('USER_MESSAGE', JSON.stringify(list))
  }
}

//保存
const handleSave = () => {
  if (!authStore.token) {
    jumpUser()
  }
  identifyApi.editSetting({
    uid: userInfo.value.id,
    train: train.value,
    openTrain: openTrain.value,
    brand: activeTag.value.length ? activeTag.value.join(',') : '',
    cate: activeCategory.value.length ? activeCategory.value.join(',') : ''
  }).then((res) => {
    if (res.code === 200) {
      showToast('已保存属性')
      getUserMes()
      setCache('PROPERTIES', true);
      emit('save')
      hide()
    }
  })
}

const handleCancel = () => {
  activeTag.value = []
  activeCategory.value = []
  setCache('PROPERTIES', true);
  // hide()
}

const handleClose = () => {
  setCache('PROPERTIES', true);
}

const show = async () => {
  if(authStore.token) {
    let res = await identifyApi.getSetting({uid: userInfo.value.id})
    if (res.code === 200) {
      let list = res.data?.list
      if (list.brand) {
        activeTag.value = list.brand.split(',')
            .map(s => Number(s.trim()));
      }
      if (list.cate) {
        activeCategory.value = list.cate.split(',')
            .map(s => Number(s.trim()));
      }
      train.value = list.train
      openTrain.value = list.openTrain
    } else {
      activeTag.value = []
      activeCategory.value = []
    }
    attributeShow.value = true
  }
}

const hide = () => {
  attributeShow.value = false
}


defineExpose({show, hide})

</script>

<style scoped lang="scss">
.attribute-popup {
  margin: 15px 12px 20px;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .tips {
    color: #666666;
    font-size: 13px;
    font-weight: 400;
  }

  .classify {
    padding: 20px 0 12px;
    color: #3D3D3D;
    font-size: 16px;
  }

  .study-container {
    color: #3D3D3D;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .tag-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    column-gap: 10px;
    row-gap: 10px;

    .tag-item {
      position: relative;
      width: calc((100% - 30px) / 4);
      color: #3D3D3D;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      padding: 12px 0;
      border-radius: 5px;
      background-color: #F5F5F5;

      .check-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        display: flex;
        border-radius: 5px 0 5px 0;
        background-color: #93C3BF;

        .check-img {
          width: 6px;
          height: 6px;
          padding: 3px;
        }
      }
    }

    .active-item {
      color: #478E87;
      background-color: #DFF7F6;
    }

    .active-item::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 200%;
      height: 200%;
      border-radius: 10px;
      border: 1px solid #93C3BF;
      transform: scale(0.5);
      transform-origin: 0 0;
      box-sizing: border-box;
    }
  }

  .category-container {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    column-gap: 57px;
    row-gap: 12px;

    .category-item {
      width: calc((100% - 0px) / 3);
      color: #3D3D3D;
      font-size: 13px;
      font-weight: 400;
      display: flex;
      align-items: center;

      .item-icon {
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }
    }
  }

  .footer-box {
    margin: 30px 0 30px;
    display: flex;
    justify-content: space-between;
    font-size: 16px;

    .left-btn {
      padding: 11px 68px;
      color: #478E87;
      border-radius: 22px;
      background-color: #DFF7F6;
    }

    .right-btn {
      padding: 11px 68px;
      color: #FFFFFF;
      border-radius: 22px;
      background-color: #478E87;
    }
  }
}

.van-divider {
  margin: 12px 0;
}
</style>