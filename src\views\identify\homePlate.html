<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中羽在线 - 鉴定平台</title>
    <link href="https://cdn.jsdelivr.net/npm/vant@4.9.0/lib/index.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vant@4.9.0/lib/vant.min.js"></script>
    <script src="https://app.badmintoncn.com/public/static/dest/js/libs/magjs-x.js?version=5.6.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #f8f8f8;
            padding: 0 12px;
        }

        .plate-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .swiper-box {
            /*margin: 20px 0;*/
            background: linear-gradient(180deg, #A159FF 0%, #E5C8FF 100%);
            border-radius: 10px;
            overflow: hidden;
            /*box-shadow: 0 4px 12px rgba(161, 89, 255, 0.2);*/
        }

        .title-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
        }

        .title-box .left-icon {
            width: 138px;
            height: 18px;
        }

        .title-box .right-box {
            display: flex;
            align-items: center;
            color: #FFFFFF;
            font-size: 13px;
            font-weight: 400;
            cursor: pointer;
        }

        .title-box .right-box span {
            margin-right: 4px;
        }

        .title-box .right-icon {
            width: 10px;
            height: 10px;
        }

        .swiper-content {
            height: 161px;
            padding: 0 10px;
        }

        .list-content {
            display: flex;
            justify-content: flex-start;
            flex-wrap: nowrap;
            column-gap: 9px;
            row-gap: 12px;
        }

        .list-box {
            width: calc((100% - 9px) / 2);
            padding-left: 1px;
        }

        .image-bg {
            position: relative;
            width: 100%;
            height: 161px;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .image-box {
            width: 100%;
            height: 161px;
            object-fit: cover;
            border-radius: 5px;
            background-color: #f0f0f0;
        }

        .image-top {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            z-index: 2;
        }

        .top-people {
            display: flex;
            align-items: center;
            padding: 3px 8px 3px 3px;
            border-radius: 5px 0 5px 0;
            color: white;
            font-size: 10px;
            font-weight: bold;

        }

        .top-people .jd-icon {
            /*width: 14px;*/
            /*height: 14px;*/
            /*padding-right: 4px;*/
            width: 9px;
            height: 10px;
            margin-right: 4px;
        }

        .top-ratio {
            padding: 3px 6px;
            border-radius: 0 0 5px 5px;
            color: white;
            font-size: 10px;
            font-weight: bold;
            margin-left: 1px;
            display: flex;
            align-items: center;
        }

        .top-ratio .per-text {
            font-size: 8px;
        }

        .image-top-right {
            position: absolute;
            top: 0;
            right: 0;
            color: #FFFFFF;
            font-size: 13px;
            padding: 3px 6px;
            background-color: #9673FF;
            border-radius: 0 5px 0 5px;
            z-index: 2;
        }

        .background-layer {
            position: absolute;
            display: flex;
            align-items: center;
            bottom: 0;
            height: 28px;
            width: 100%;
            border-radius: 0 0 5px 5px;
            background: rgba(0, 0, 0, 0.1);
            filter: blur(20px);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
        }

        .verified-icon {
            position: absolute;
            top: 46%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 110px;
            height: 110px;
            z-index: 1;
            opacity: 0.8;
            border-radius: 50%;
        }

        .image-bot {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: space-between;
            bottom: 0;
            width: 100%;
            border-radius: 0 0 5px 5px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            background: rgba(88, 88, 88, 0.3);
            z-index: 2;
            padding: 5px;
        }

        .flex-vc {
            display: flex;
            align-items: center;
        }

        .bot-brand {
            width: 40px;
            height: 18px;
            border-radius: 3px;
            margin-right: 5px;
        }

        .bot-text {
            color: #FFFFFF;
            font-weight: 600;
            font-size: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
        }

        .bot-time {
            color: #FFFFFF;
            font-size: 10px;
            font-weight: 600;
        }

        .indicators-box {
            display: flex;
            justify-content: center;
            padding: 8px 0;
        }

        .dot {
            width: 5px;
            height: 2px;
            margin-right: 3px;
            background-color: #FFFFFF;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .active-dot {
            background-color: #FFFFFF;
            opacity: 1;
        }

        .good-book {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 10px 0 0;
            padding: 0 10px;
            background-color: #FFFFFF;
            border-radius: 10px;
            height: 60px;
            /*box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);*/
            cursor: pointer;
        }

        .good-left {
            display: flex;
            align-items: center;
        }

        .book-icon {
            width: 36px;
            height: 36px;
            margin-right: 12px;
        }

        .left-title {
            color: #3D3D3D;
            font-size: 15px;
            font-weight: 500;
        }

        .left-tips {
            color: #999999;
            font-size: 12px;
            font-weight: 400;
        }

        .good-right {
            display: flex;
            align-items: center;
            color: #999999;
            font-size: 13px;
        }

        .pr-2 {
            padding-right: 4px;
        }
    </style>
</head>
<body>
<div id="app">
    <div class="plate-container">
        <div class="swiper-box" @click="handleMore">
            <div class="title-box">
                <img class="left-icon" src="https://www.badmintoncn.com/cbo_ea/img/homePlateTitle.png"/>
                <div class="right-box">
                    <span>更多</span>
                    <van-icon name="arrow" color="#FFFFFF" size="12"/>
                </div>
            </div>
            <div class="swiper-content">
                <van-swipe lazy-render :autoplay="3000" :show-indicators="false" @change="handleSwitch">
                    <van-swipe-item class="list-content" v-for="(arr, index) in dataList" :key="index">
                        <div class="list-box" v-for="val in arr">
                            <div class="image-bg">
                                <van-image v-if="val.picture" class="image-box" fit="cover"
                                           :src="`${val.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`">
                                    <template v-slot:loading>
                                        <van-loading type="spinner" size="20"/>
                                    </template>
                                </van-image>
                                <img v-if="!val.picture.length" class="image-box"
                                     src="https://www.badmintoncn.com/cbo_ea/img/hollowIcon.png"/>
                                <div class="image-top" v-if="val.real + val.fake !== 0 && !userMes?.train">
                                    <div class="top-people"
                                         :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                                        <img class="jd-icon"
                                             src="https://www.badmintoncn.com/cbo_ea/img/shieldIcon.png"/>
                                        <div class="pr-4">{{ val.real + val.fake }}</div>
                                    </div>
                                    <div class="top-ratio" v-if="val.real + val.fake >= 2"
                                         :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                                        {{ getDominantPercentage(val.real, val.fake) }}
                                        <span class="per-text">%</span>
                                    </div>
                                </div>
                                <div v-if="val?.train" class="image-top-right">训</div>
                                <div class="background-layer"></div>
                                <img v-if="val.appraisal !== null" class="verified-icon"
                                     src="https://www.badmintoncn.com/cbo_ea/img/verifiedIcon.png"/>
                                <div class="image-bot">
                                    <div class="flex-vc">
                                        <img v-if="val.brand === '其他'" class="bot-brand"
                                             src="https://www.badmintoncn.com/cbo_ea/img/brandOther.png">
                                        <img v-else class="bot-brand" :src="val.brandLogo"/>
                                        <div class="bot-text">{{ `${val.cate}` }}</div>
                                    </div>
                                    <div class="bot-time">{{ val?.createTime }}</div>
                                </div>
                            </div>
                        </div>
                    </van-swipe-item>
                </van-swipe>
            </div>
            <div class="indicators-box">
                <div v-for="(item, index) in dataList" :key="index">
                    <div class="dot" :class="{'active-dot' : activeTab === index}"></div>
                </div>
            </div>
        </div>
        <div v-if="false" class="good-book" @click="handleJump">
            <div class="good-left">
                <img class="book-icon" src="https://www.badmintoncn.com/cbo_ea/img/bookIcon.png"/>
                <div class="left-title">
                    <div>鉴定好文</div>
                    <div class="left-tips">了解更多鉴定知识</div>
                </div>
            </div>
            <div class="good-right">
                <div class="pr-2">去看看</div>
                <van-icon name="arrow" size="12"/>
            </div>
        </div>
    </div>
</div>

<script>
    const {createApp, ref, computed, onMounted} = Vue;
    const {Swipe, SwipeItem, Image, Loading, Icon} = vant;
    const loadScript = (src) => {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    };

    // 页面加载完成后加载非关键资源
    window.addEventListener('load', () => {
        loadScript('https://app.badmintoncn.com/public/static/dest/js/libs/magjs-x.js?version=5.6.0');
    });
    createApp({
        setup() {
            // 模拟数据
            const dataList = ref([]);

            const activeTab = ref(0);
            const userMes = ref({});

            // 获取背景颜色
            const getBackgroundColor = (real, fake) => {
                if (real + fake === 0) return '';
                if (real > fake) {
                    return '#00B578';
                } else if (fake > real) {
                    return '#FA5151';
                } else {
                    return '#676f6e';
                }
            };

            // 获取主导方的百分比
            const getDominantPercentage = (real, fake) => {
                const total = real + fake;
                if (total === 0) return '';
                if (real > fake) {
                    return `${Math.round((real / total) * 100)}`;
                } else if (fake > real) {
                    return `${Math.round((fake / total) * 100)}`;
                } else {
                    return '50';
                }
            };

            // 处理更多按钮点击
            const handleMore = () => {
                // alert('跳转到更多鉴定页面');
                const apiUrl = 'https://www.badmintoncn.com/cbo_ea_dev/dist/index.html#'
                window.mag.newWin(`${apiUrl}/identify/index?mag_hide_progress=1`);
            };

            const handleSwitch = (index) => {
                activeTab.value = index
            }

            // 处理跳转按钮点击
            const handleJump = () => {
                if (window.navigator.appVersion.toLowerCase().indexOf('magappx') != -1) {
                    window.mag.newWin(`https://www.badmintoncn.com/thread.php?a=list&classid=35&mag_hide_progress=1`);
                } else {
                    let path = 'https://www.badmintoncn.com/thread.php?a=list&classid=35&mag_hide_progress=1'
                    window.open(path, '_blank')
                }
            };


            const arrayTo2D = (arr, columns) => {
                let result = [];
                for (let i = 0; i < arr.length; i += columns) {
                    result.push(arr.slice(i, i + columns));
                }
                return result;
            };


            // 从服务器获取数据
            const fetchData = async () => {
                try {
                    const params = {
                        // uid: userInfo?.value && userInfo.value.id ? userInfo.value.id : null,
                        uid: null,
                        page: 1,
                        pageSize: 6,
                        order: 2
                    };

                    const response = await axios.get('https://cbo.badmintoncn.com/api/ea/search', {
                        params: params
                    });

                    if (response.data.code === 200) {
                        const list = response.data.data.list;
                        const arrayTo2D = (arr, columns) => {
                            let result = [];
                            for (let i = 0; i < arr.length; i += columns) {
                                result.push(arr.slice(i, i + columns));
                            }
                            return result;
                        };
                        dataList.value = arrayTo2D(list, 2);
                    }
                } catch (error) {
                    console.error('Error fetching data:', error);
                }
            };


            onMounted(() => {
                // 模拟获取数据
                fetchData();
                console.log('组件已挂载');
            });

            return {
                dataList,
                activeTab,
                userMes,
                getBackgroundColor,
                getDominantPercentage,
                handleMore,
                handleSwitch,
                handleJump
            };
        }
    })
        .use(Swipe)
        .use(SwipeItem)
        .use(Image)
        .use(Loading)
        .use(Icon)
        .mount('#app');
</script>
</body>
</html>