<template>
  <van-overlay :show="visible" z-index="999999">
    <div class="loading-page">
      <div class="container">
        <div class="icon-box">
          <img class="circle-icon" :src="circleIcon"/>
        </div>
        <img class="wind-icon" :src="windIcon" />
      </div>
      <div class="loading-text">{{discText}}</div>
    </div>
  </van-overlay>
   <div></div>
</template>

<script setup>
import {ref} from 'vue'
import windIcon from "@/assets/images/loading/wind.png"
import circleIcon from "@/assets/images/loading/circle.png"

const visible = ref(false)

const props = defineProps({
   top: {
     type: Number,
     default: 30,
   },
   discText: {
     type: String,
     default: '发布中...'
   }
})


const show = () => {
  visible.value = true
}

const hide = () => {
  visible.value = false
}

defineExpose({ show , hide}) // 提供 openModal 方法

</script>
<style lang="scss" scoped>
.loading-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.container {
  position: relative;
  width: 111px;
  //height: 111px;
  margin-bottom: 10px;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-box {
  position: relative;
}

.circle-icon {
  width: 53px;
  height: 53px;
  border-radius: 50%;
  animation: rotate 1s infinite linear;
}

.wind-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  width: 25px;
  height: 25px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 16px;
}
.van-overlay {
  background: rgba(255, 255, 255, 0.7) !important;
}
</style>