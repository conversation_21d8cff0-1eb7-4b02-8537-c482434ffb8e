<template>
  <van-popup
      v-model:show="appUpOverlay"
      round
      position="bottom"
  >
    <div class="btn-container">
      <div class="type-line"/>
      <div class="type-btn"  @click="handleAppChange('camera')">拍摄上传</div>
      <div class="type-line"/>
      <div class="type-btn"  @click="handleAppChange('album')">从相册上传</div>
      <div class="division"/>
      <div class="type-btn" @click.stop="appUpOverlay = false">取消</div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref} from 'vue'
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();

const appUpOverlay = ref(false)

const props = defineProps({
  item: {
    type: Object,
    default: () => {
    }
  },
  type: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['appChange'])


const handleAppChange = (type) => {
  emit('appChange', type)
}

// 暴露方法
const open = () => appUpOverlay.value = true
const hide = () => appUpOverlay.value = false


defineExpose({open, hide})
</script>

<style scoped lang="scss">
.btn-container {
  .type-btn {
    color: #333333;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    padding: 20px 0;
  }

  .type-line {
    margin: 0 20px;
    height: 1px;
    background-color: #F5F5F5;
  }

  .division {
    height: 5px;
    background-color: #F5F5F5;
  }
}
</style>