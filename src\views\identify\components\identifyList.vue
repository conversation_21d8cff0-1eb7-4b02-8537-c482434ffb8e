<template>
  <div class="identify-list-page">
    <div class="pb-10">
      <TitleTop title="求鉴装备" :state="state" :order="order" :user-mes="userMes" :switchIcon="switchIcon" :switchType="switchType"
                @change="handleScreen" @sort="handleSort" @compose="handleCompose" @click="onCLick"/>
    </div>
    <List v-if="!switchType" :listType="listType" :tableList="tableList" :equipList="equipRankingList"/>
    <gridList v-if="switchType"  :listType="listType" :tableList="tableList" :equipList="equipRankingList"/>
  </div>
</template>

<script setup>
import {ref, onMounted, computed, nextTick, watch} from 'vue'
import TitleTop from "@/components/titleTop.vue";
import List from "@/views/identify/components/list.vue"
import gridList from "@/views/identify/components/gridList.vue"
import avatarIcon from "@/assets/images/identify/avatarIcon.png"
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();
import {useRouter} from "vue-router";
import identifyApi from "@/services/identify.js";
import {getCache} from "@/utils/cache.js";

const router = useRouter()


const switchType = computed(() => {
  return authStore.switchType
})

const userInfo = computed(() => {
  return authStore.userInfo
})

const clickLogoCount = ref(0)
const loading = ref(false)
const userMes = ref()
const rankingType = ref(1)


const props = defineProps({
  listType: {
    type: String,
    default: 'identify'
  },
  tableList: {
    type: Array,
    default: () => []
  },
  state: {
    type: Number,
    default: 0,
  },
  order: {
    type: Number,
    default: 1,
  },
  switchIcon: {
    type: Boolean,
    default: false
  }
})

watch(() => props.tableList,
    () => {
      userMes.value = JSON.parse(getCache('USER_MESSAGE')) || {}
    })



const emit = defineEmits(['change', 'sort'])


const handleCompose = () => {
  authStore.switchType = !authStore.switchType
}


const equipRankingList = ref([])


const onCLick = () => {
  clickLogoCount.value++
  if (clickLogoCount.value === 10) {
    // new Vconsole()
  }
}

const handleScreen = (val) => {
  emit('change', val)
}

const handleSort = (val) => {
  emit('sort', val)
}

const getPageList = () => {
  let paramsCopy = {
    page: 1,
    pageSize: 30,
    type: 4,
  };
  identifyApi.getRanking(paramsCopy).then((res) => {
    if (res.code === 200) {
      let list = res.data.list
      equipRankingList.value = list.slice(0,3)
    }
  })
}

onMounted(() => {
  getPageList()
})


</script>

<style scoped lang="scss">
.identify-list-page {
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  padding: 0 12px;
}
</style>