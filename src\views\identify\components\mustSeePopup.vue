<template>
  <van-popup
      v-if="mustSeeShow"
      v-model:show="mustSeeShow"
      round
      position="bottom"
  >
    <div class="must-popup">
      <div class="title-box">
        <div></div>
        <div>求鉴必看</div>
        <img class="close-icon" :src="closeIcon" @click="hide"/>
      </div>
      <div class="content-box">
         <div class="content-list" v-for="(item, index) in list" :key="index" @click="jumpChange(item.id)">
           <div class="tag-box">版主推荐</div>
           <div class="content-text">{{item.title}}</div>
         </div>
      </div>
      <div class="footer-btn" @click="changeKnow">
        我知道了
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref} from "vue";
import closeIcon from '@/assets/images/common/closeIcon.png'
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();

const mustSeeShow = ref(false)

const list = ref([])


const emit = defineEmits(['close'])

const getEaHelpList = async () => {
   let res = await identifyApi.getEaHelp({})
   if(res.code === 200) {
    list.value = res.data.list
  }
}


const jumpChange = (id) => {
  if(!authStore.phone) {
    window.location.href = `https://m2.badmintoncn.com/mag/circle/v1/forum/threadWapPage?tid=${id}&themecolor=478e87&circle_id=114`;
  } else {
    window.mag.newWin(`https://m2.badmintoncn.com/mag/circle/v1/forum/threadWapPage?&mag_hide_progress=1&tid=${id}&themecolor=478e87&circle_id=114`)
  }
}

const changeKnow = () =>{
  hide()
 emit('close')
}

const show = () => {
  mustSeeShow.value = true
  getEaHelpList()
}
const hide = () => {
  mustSeeShow.value = false
}


defineExpose({show, hide})
</script>

<style lang="scss" scoped>
.must-popup {
  margin: 0 12px 20px;
  min-height: 300px;
  box-sizing: border-box;
  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;
    font-weight: 600;
    margin: 15px 0 25px;
    .close-icon {
      width: 20px;
      height: 20px;
    }
  }
  .content-box {
    .content-list {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #3D3D3D;
      margin-bottom: 25px;
      .tag-box {
        flex-shrink: 0;
        font-size: 10px;
        color: #FFFFFF;
        border-radius: 3px;
        padding: 2px 4px;
        margin-right: 5px;
        background-color: #FA5151;
      }
      .content-text {
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .footer-btn {
    color: #FFFFFF;
    font-size: 16px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #478E87;
  }
}
</style>