/**
 * 从本地存储中获取缓存数据
 * @param {string} key - 缓存数据的键名
 * @returns {any} - 返回缓存的数据，如果不存在则返回 null
 */
export const getCache = (key) => {
    try {
        const value = localStorage.getItem(key);
        return value !== null ? JSON.parse(value) : null;
    } catch (e) {
        console.error('Error getting cache:', e);
        return null;
    }
};

/**
 * 设置本地存储中的缓存数据
 * @param {string} key - 缓存数据的键名
 * @param {any} value - 需要缓存的数据
 * @returns {void}
 */
export const setCache = (key, value) => {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (e) {
        console.error('Error setting cache:', e);
    }
};

/**
 * 从本地存储中移除指定键名的缓存数据
 * @param {string} key - 缓存数据的键名
 * @returns {void}
 */
export const removeCache = (key) => {
    try {
        localStorage.removeItem(key);
    } catch (e) {
        console.error('Error removing cache:', e);
    }
};

/**
 * 清空本地存储中的所有缓存数据
 * @returns {void}
 */
export const clearCache = () => {
    try {
        localStorage.clear();
    } catch (e) {
        console.error('Error clearing cache:', e);
    }
};

/**
 * 设置本地存储中cookie 用来做登录
 * @param {string} key - 缓存数据的键名
 * @param {any} value - 需要缓存的数据
 * @returns {void}
 */

export const setCookie = (name, value) => {
    // let setKey = name + "=" + value
    // console.log('setKey', setKey)
    document.cookie = name + "=" + value + ";"
}


/**
 * 从本地存储中获取cookie
 * @param {string} key - 缓存数据的键名
 * @returns {any} - 返回缓存的数据，如果不存在则返回 null
 */

export const getCookie = (cookieName) => {
    const strCookie = document.cookie
    const cookieList = strCookie.split(';')
    for (let i = 0; i < cookieList.length; i++) {
        const arr = cookieList[i].split('=')
        if (cookieName === arr[0].trim()) {
            return arr[1]
        }
    }

    return ''
}

// export const getCookie = () => {
//     const strCookie = document.cookie
//     let value = strCookie.split('=')[1]
//     console.log('value', value)
//     return value
//     // return strCookie.split('=')[1];
// }