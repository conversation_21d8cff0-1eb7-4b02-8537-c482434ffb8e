<template>
  <van-popup
      v-if="popShow"
      v-model:show="changeOverlay"
      round
      position="bottom"
  >
    <div class="btn-container">
      <!-- 图片操作 -->
      <div class="type-btn" @click="handleUploadType('camera')">拍照</div>
      <div class="type-line"/>
      <div class="type-btn" @click="handleUploadType('video-camera')">拍视频</div>
      <div class="type-line"/>
      <div class="type-btn" @click="handleUploadType('album')">选择图片</div>
      <div class="type-line"/>
      <div class="type-btn" @click="handleUploadType('video-album')">选择视频</div>

      <!-- 取消 -->
      <div class="division"/>
      <div class="type-btn cancel-btn" @click.stop="changeOverlay = false">取消</div>
    </div>
  </van-popup>

  <!-- 图片相关 Input -->
  <input
      ref="cameraInput"
      type="file"
      hidden
      accept="image/*"
      capture="environment"
      @change="handleFileSelect"
  >
  <input
      ref="albumInput"
      type="file"
      hidden
      :multiple="albumMultiple"
      accept="image/*"
      @change="handleFileSelect"
  >

  <!-- 视频相关 Input -->
  <input
      ref="videoCameraInput"
      type="file"
      hidden
      accept="video/*"
      capture="environment"
      @change="handleFileSelect"
  >
  <input
      ref="videoAlbumInput"
      type="file"
      hidden
      accept="video/*"
      @change="handleFileSelect"
  >
</template>

<script setup>
import {ref, computed} from 'vue'
import {showLoadingToast, closeToast} from 'vant'
import identifyApi from "@/services/identify.js";
import commonApi from "@/services/common.js";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();

const emit = defineEmits(['upload-success', 'upload-error'])
const cameraInput = ref(null)
const albumInput = ref(null)
const videoCameraInput = ref(null)
const videoAlbumInput = ref(null)
const changeOverlay = ref(false)


const userInfo = computed(() => {
  return authStore.userInfo
})


const props = defineProps({
  popShow: {
    type: Boolean,
    default: true
  },
  maxVideoSize: {
    type: Number,
    default: 100 * 1024 * 1024 // 100MB
  },
  albumMultiple: {
    type: Boolean,
    default: false,
  },
  imageArr: {
    type: Array,
    default: () => []
  }
})

// 压缩配置
const compressConfig = {
  maxWidth: 800,
  quality: 0.7,
  mimeType: 'image/jpeg'
}

const handleUploadType = (type) => {
  changeOverlay.value = false
  switch (type) {
    case 'camera':
      cameraInput.value.click()
      break
    case 'album':
      albumInput.value.click()
      break
    case 'video-camera':
      videoCameraInput.value.click()
      break
    case 'video-album':
      videoAlbumInput.value.click()
      break
  }
}

// 提取视频第一帧的方法
const extractVideoThumbnail = (file) => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')

    // 设置视频源
    video.src = URL.createObjectURL(file)
    video.preload = 'metadata'
    video.muted = true
    video.playsInline = true

    // 当视频元数据加载完成后
    video.onloadedmetadata = () => {
      video.currentTime = 0.1 // 跳转到视频的第一帧（0.1秒）
    }

    // 当视频帧准备好时
    video.onseeked = () => {
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      const ctx = canvas.getContext('2d')
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      // 导出第一帧为 base64 图片
      const thumbnail = canvas.toDataURL('image/jpeg')
      resolve(thumbnail)
    }

    // 错误处理
    video.onerror = (error) => {
      reject(error)
    }
  })
}

const handleFileSelect = async (e) => {
  const files = Array.from(e.target.files);
  if (files.length === 0) return;
  showLoadingToast({message: '处理中...', forbidClick: true})
  // 获取上传限制信息
  const {attachAll, todayAttach} = await imgCount();

  // 检查是否达到上传限制
  if (attachAll > 0 && todayAttach >= attachAll) {
    showToast('今日上传数量已达上限');
    return;
  }

  // 检查父组件图片数量限制
  const currentCount = props.imageArr.length;
  if (currentCount >= 6) {
    showToast('最多上传6张图片');
    return;
  }

  if (files.length >= 6) {
    showToast('最多上传6张图片');
  }

  // 如果选择的文件加上已上传的文件超过6张，则截取前6张
  const filesToUpload = files.slice(0, 6 - currentCount);

  try {
    const isImage = filesToUpload[0].type.startsWith('image/');
    const isVideo = filesToUpload[0].type.startsWith('video/');

    if (isImage) {
      // 如果是图片，进行压缩
      const compressedFiles = await Promise.all(filesToUpload.map(file => compressImage(file)));
      for (const compressedFile of compressedFiles) {
        const formData = new FormData();
        formData.append('file', compressedFile);
        try {
          const res = await identifyApi.uploadFile(userInfo.value.id, formData);
          console.log('res', res);
          emit('upload-success', res.data);
        } catch (error) {
          console.error('上传失败', error);
          emit('upload-error', error);
        }
      }
    } else if (isVideo) {
      // 如果是视频，提取第一帧并上传
      const thumbnail = await extractVideoThumbnail(filesToUpload[0]);
      emit('upload-success', {
        videoUrl: filesToUpload[0],
        imagePath: thumbnail,
      });
    } else {
      throw new Error('不支持的文件类型');
    }
  } catch (error) {
    emit('upload-error', error);
  } finally {
    e.target.value = ''; // 清空已选文件
  }
};
/**/


const imgCount = async () => {
  // 判断上传文件数量是否达到每日上限
  let res = await commonApi.getGold({id: authStore.userInfo.id});
  if (res.code === 200) {
    return res.data.list;
  }
  return {attachAll: 0, todayAttach: 0}; // 默认值，根据实际情况调整
};


// 处理文件选择
// const handleFileSelect = async (e) => {
//   const files = Array.from(e.target.files)
//   if (files.length === 0) return
//
//   showLoadingToast({ message: '处理中...', forbidClick: true })
//
//   try {
//     const processedFiles = await Promise.all(
//         files.map(async file => {
//           console.log('file', file)
//           // 视频文件
//           if (file.type.startsWith('video/') && file.size > props.maxVideoSize) {
//             throw new Error(`视频文件大小不能超过${props.maxVideoSize/1024/1024}MB`)
//           } else if (file.type.startsWith('video/')) {
//             return file
//           }
//           // 图片文件进行压缩
//           if (file.type.startsWith('image/')) {
//             return await compressImage(file)
//           }
//           throw new Error('不支持的文件类型')
//         })
//     )
//     emit('upload-success', processedFiles)
//   } catch (error) {
//     emit('upload-error', error)
//   } finally {
//     e.target.value = ''
//     closeToast()
//   }
// }


// 图片压缩方法（保持不变）
const compressImage = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const img = await new Promise((resolve, reject) => {
          const image = new Image()
          image.onload = () => resolve(image)
          image.onerror = reject
          image.src = e.target.result
        })

        const canvas = document.createElement('canvas')
        const ratio = Math.min(compressConfig.maxWidth / img.width, 1)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio

        canvas.getContext('2d').drawImage(img, 0, 0, canvas.width, canvas.height)

        canvas.toBlob(blob => {
          resolve(new File([blob], file.name, {
            type: compressConfig.mimeType
          }))
        }, compressConfig.mimeType, compressConfig.quality)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

// 暴露方法
const open = (type) => {
  if (props.popShow) {
    changeOverlay.value = true
  } else if (type === 'camera') {
    albumInput.value.click()
  } else if (type === 'video') {
    videoAlbumInput.value.click()
  }
}
const hide = () => changeOverlay.value = false
defineExpose({open, hide})
</script>

<style scoped lang="scss">
.btn-container {
  .type-btn {
    color: #333333;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    padding: 20px 0;
  }

  .type-line {
    margin: 0 20px;
    height: 1px;
    background-color: #F5F5F5;
  }

  .division {
    height: 5px;
    background-color: #F5F5F5;
  }
}
</style>