import loveIcon from "@/assets/images/common/loveIcon.png";
import jewelIcon from "@/assets/images/common/jewelIcon.png";
import goldenShield from "@/assets/images/common/goldenShield.png";
import crownIcon from "@/assets/images/common/crownIcon.png";

export const getGradeBoxClass = (points) => {
    const score = Number(points);
    if (score >= 5 && score <= 1200) return 'grade-box-red';
    if (score > 1200 && score <= 20000) return 'grade-box-blue';
    if (score > 20000 && score <= 66000) return 'grade-box-yellow';
    if (score > 66000) return 'grade-box-purple';
    return '';
}

export const getGradeBoxTag = (points) => {
    const score = Number(points);
    if (score >= 5 && score <= 1200) return loveIcon;
    if (score > 1200 && score <= 20000) return jewelIcon;
    if (score > 20000 && score <= 66000) return goldenShield;
    if (score > 66000) return crownIcon;
    return '';
}

export const getLevelByExperience = (points) => {
    const exp = Number(points);
    if (exp > 5 && exp <= 50) {
        return "热心一级";
    } else if (exp > 50 && exp <= 100) {
        return "热心二级";
    } else if (exp > 100 && exp <= 300) {
        return "热心三级";
    } else if (exp > 300 && exp <= 600) {
        return "热心四级";
    } else if (exp > 600 && exp <= 1200) {
        return "热心五级";
    } else if (exp > 1200 && exp <= 2500) {
        return "蓝钻一级";
    } else if (exp > 2500 && exp <= 5000) {
        return "蓝钻二级";
    } else if (exp > 5000 && exp <= 9000) {
        return "蓝钻三级";
    } else if (exp > 9000 && exp <= 14000) {
        return "蓝钻四级";
    } else if (exp > 14000 && exp <= 20000) {
        return "蓝钻五级";
    } else if (exp > 20000 && exp <= 27000) {
        return "金盾一级";
    } else if (exp > 27000 && exp <= 35000) {
        return "金盾二级";
    } else if (exp > 35000 && exp <= 44000) {
        return "金盾三级";
    } else if (exp > 44000 && exp <= 54000) {
        return "金盾四级";
    } else if (exp > 54000 && exp <= 66000) {
        return "金盾五级";
    } else if (exp > 66000 && exp <= 80000) {
        return "紫冠一级";
    } else if (exp > 80000 && exp <= 100000) {
        return "紫冠二级";
    } else if (exp > 100000 && exp <= 120000) {
        return "紫冠三级";
    } else if (exp > 120000 && exp <= 150000) {
        return "紫冠四级";
    } else if (exp > 150000) {
        return "紫冠五级";
    }
}
