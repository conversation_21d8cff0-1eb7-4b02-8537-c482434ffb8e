<template>

  <div class="content">
    <!--文本框-->
    <div
        ref="divRef"
        class="editor"
        :contenteditable="isEditable"
        @keyup="handleKeyUp"
        @keydown="handleKeyDown"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        @touchstart="handleTouchStart"
    >
      <span class="tips"
            v-if="showPlaceholder && tips">请补充商品名及价格有助于更快完成鉴定，请不要说出购买商家，最后可主动求助他人鉴定@通知最多@12位热心球友</span>
    </div>
    <!--选项-->
    <MentionPopup ref="mentionPopupRef" @addUser="handleUser"/>

  </div>

</template>

<script lang="ts" setup>

import {onMounted, ref} from 'vue';
import MentionPopup from "@/views/identify/components/mentionPopup.vue";
import sandText from "./sendText.vue"

const showPlaceholder = ref(true);
const mentionPopupRef = ref()
const userList: any = ref([]) //用户数据列表
const node = ref('') // 获取到节点
const user = ref('') // 选中项的内容
const endIndex = ref('') // 光标最后停留位置


const divRef: any = ref(null)

const props = defineProps({
  tips: {
    type: Boolean,
    default: true
  },
  expandType: {
    type: Boolean,
    default: false
  },
  isEditable: {
    type: Boolean,
    default: true
  }
})


// const handleTouchStart = (event) => {
//   event.preventDefault();
//   // 强制聚焦
//   divRef.value.focus();
// };

const handleTouchStart = (event) => {
  // 延迟聚焦以避免立即弹起
  // setTimeout(() => {
  //   // 强制聚焦
  //   divRef.value.focus();
  //
  //   // 尝试恢复光标到之前的位置
  //   const selection = window.getSelection();
  //   if (selection.rangeCount > 0) {
  //     const range = selection.getRangeAt(0);
  //     const cursorPosition = range.endOffset;
  //
  //     // 创建一个新的范围，从div的开始到光标的位置
  //     const newRange = document.createRange();
  //     newRange.setStart(divRef.value.firstChild, cursorPosition);
  //     newRange.setEnd(divRef.value.firstChild, cursorPosition);
  //
  //     // 清除现有的选择并设置新的范围
  //     selection.removeAllRanges();
  //     selection.addRange(newRange);
  //   }
  // }, 0); // 使用0延迟来尽快执行，但不在当前事件循环中
};

const handleInput = () => {
  if (!props.tips) {
    adjustHeight()
  }
}

const adjustHeight = () => {
  const div = divRef.value;
  div.style.height = 'auto'; // 重置高度
  div.style.height = div.scrollHeight + 'px'; // 设置为内容高度
}


// 获取焦点事件
const handleFocus = () => {
  divRef.value.focus();
  showPlaceholder.value = false;
  emit('focus')
};

// 失去焦点事件
const handleBlur = () => {
  showPlaceholder.value = divRef.value?.innerText?.trim() === '';
};

// 数组去重

const distinct = (arr: any, key: any) => {

  var newObj: any = {}

  var newArr: any = []

  for (var i = 0; i < arr.length; i++) {

    var item = arr[i]

    if (!newObj[item[key]]) {

      newObj[item[key]] = newArr.push(item)

    }

  }

  return newArr

}

//格式化

const escape2Html = (str: any) => {

  // 格式化

  var arrEntities: any = {lt: '<', gt: '>', nbsp: ' ', amp: '&', quot: '"'}

  return str.replace(/&(lt|gt|nbsp|amp|quot);/gi, function (all: any, t: any) {

    return arrEntities[t]

  })

}

//获取输入的@用户的信息

const logA = () => {
  const text = divRef.value.innerHTML
  const text2 = escape2Html(text)
  let list = []
  for (let index = 0; index < userList.value.length; index++) {
    if (text2.includes(`"id":"${userList.value[index].id}"`)) {
      list.push(userList.value[index])
    }
  }

  list = distinct(list, 'id')//获取到@的id和name
  // console.log(divRef.value.innerText, list)
  return {
    text: divRef.value.innerText,
    list,
  }

}

const handleAddUser = () => {
  // 模拟输入@符号
  const div = divRef.value;
  const selection = window.getSelection();
  const range = document.createRange();

  // 如果当前有选中的范围，则使用该范围，否则创建一个新的范围
  if (selection.rangeCount > 0) {
    range.setStart(selection.getRangeAt(0).startContainer, selection.getRangeAt(0).startOffset);
    range.setEnd(selection.getRangeAt(0).endContainer, selection.getRangeAt(0).endOffset);
  } else {
    // 将光标设置在内容的末尾
    range.selectNodeContents(div);
    range.setStart(div, 0);
    range.setEnd(div, div.childNodes.length);
  }

  // 插入@符号
  const atSymbol = document.createTextNode('');
  range.deleteContents();
  range.insertNode(atSymbol);

  // 重新设置光标位置，放在@符号后面
  range.setStartAfter(atSymbol);
  range.setEndAfter(atSymbol);
  selection.removeAllRanges();
  selection.addRange(range);

  // 显示mentionPopupRef组件
  divRef.value.blur(); // 移除焦点
  mentionPopupRef.value.open();

  // 保存当前光标位置和节点
  node.value = atSymbol;
  endIndex.value = 0; // @符号后面，所以偏移量为0
}

const hide = () => {
  divRef.value.innerHTML = '';
  node.value = '';
  endIndex.value = '';
  userList.value = [];
  mentionPopupRef.value.hide();
  showPlaceholder.value = true;
  adjustHeight();
}
const setText = (text) => {
  showPlaceholder.value = false
  divRef.value.innerHTML += text
}
const setTextAdd = (text) => {
  showPlaceholder.value = false
  divRef.value.innerHTML = text
}

const setContent = (content) => {
  const div = divRef.value;
  div.innerHTML = ''; // 清空当前内容

  // 解析content中的@用户
  const atUsers = content.atName;
  const atUserMap = atUsers.reduce((map, user) => {
    map[user.name] = user;
    return map;
  }, {});
  // 将content.text分割成文本和@用户mention
  const parts = content.content.split(/(@\w+)/g);

  // 遍历分割后的部分，分别处理文本和@用户mention
  parts.forEach(part => {
    if (part.startsWith('@') && atUserMap[part.slice(1)]) {
      // 如果part是@用户，创建可交互的元素并插入
      const user = atUserMap[part.slice(1)];
      const userSpan = document.createElement('span');
      userSpan.textContent = user.name;
      userSpan.classList.add('at-user'); // 添加样式类
      // 可以在这里添加更多的事件监听器或样式
      div.appendChild(userSpan);
    } else {
      // 如果part是普通文本，直接插入
      div.appendChild(document.createTextNode(part));
    }
  });
};

const resetText = () => {
  divRef.value.innerHTML = '';
}

defineExpose({logA, handleAddUser, hide, handleFocus, adjustHeight, setText, setTextAdd, setContent});
const emit = defineEmits(['changeText', 'focus'])


//初始化

onMounted(() => {

  handleIn()
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    selection.removeAllRanges();
  }

})

//初始化各项数据

const handleIn = async () => {
  // divRef.value.focus()
  document.execCommand('selectAll', false, '')
  let a: any = document.getSelection()
  a.collapseToEnd()
  const node1 = getRangeNode()
  const endIndex1 = getCursorIndex()
  node.value = node1
  endIndex.value = endIndex1

}

// 获取光标位置

const getCursorIndex = () => {
  const selection: any = window.getSelection()//窗口的selection对象，当前用户选择的文本
  return selection.focusOffset // 选择开始处 focusNode 的偏移量
}

// 获取节点

const getRangeNode = () => {
  const selection: any = window.getSelection()
  return selection.focusNode // 选择的结束节点，正在聚焦的节点
}


// 是否展示 @ 匹配@

const showAt = () => {

  const node = getRangeNode() //获取当前被聚焦的节点

  if (!node || node.nodeType !== Node.TEXT_NODE) return false

  const content = node.textContent || '' //获取节点文本

  const regX = /@([^@\s]*)$/

  const match = regX.exec(content.slice(0, getCursorIndex())) //输入到 当前光标位置 匹配@

  return match && match.length === 2 //返回@符号 一个@长度为2

}

// 获取 @ 用户 @后的数据

const getAtUser = () => {

  const content = getRangeNode()?.textContent || ''

  const regX = /@([^@\s]*)$/

  const match = regX.exec(content.slice(0, getCursorIndex()))

  if (match && match.length === 2) {

    return match[1] //@ 后面跟着的字符  光标停留

  }

  return undefined

}

// 创建整个@标签

const createAtButton = (user: any) => {

  const btn = document.createElement('span')

  btn.style.display = 'inline-block'

  btn.dataset.user = JSON.stringify(user)

  btn.className = 'at-button'

  btn.contentEditable = 'false'

  btn.textContent = `@${user.name}`

  btn.style.color = 'blue'

  const wrapper = document.createElement('span')

  wrapper.style.display = 'inline-block'

  wrapper.contentEditable = 'false'

  const spaceElem = document.createElement('span')

  spaceElem.style.whiteSpace = 'pre'

  spaceElem.textContent = '\u200b'

  spaceElem.contentEditable = 'false'

  const clonedSpaceElem = spaceElem.cloneNode(true)

  wrapper.appendChild(spaceElem)

  wrapper.appendChild(btn)

  wrapper.appendChild(clonedSpaceElem)

  return wrapper

}

const replaceString = (raw: any, replacer: any) => {

  return raw.replace(/@([^@\s]*)$/, replacer)

}
const replaceAtUser = (user: any) => {
  const node1: any = node.value;
  if (node1 && user) {
    const content = node1.textContent || '';
    const endIndex1 = endIndex.value;

    const preSlice = replaceString(content.slice(0, endIndex1), '');
    const restSlice = content.slice(endIndex1);

    const parentNode = node1.parentNode;
    const nextNode = node1.nextSibling;

    const previousTextNode = new Text(preSlice);
    const nextTextNode = new Text(restSlice); // 保持原有后续内容

    const atButton = createAtButton(user); // 创建@user标签

    parentNode.removeChild(node1);

    // 插在文本框中
    if (nextNode) {
      parentNode.insertBefore(previousTextNode, nextNode);
      parentNode.insertBefore(atButton, nextNode);
      parentNode.insertBefore(nextTextNode, nextNode);
    } else {
      parentNode.appendChild(previousTextNode);
      parentNode.appendChild(atButton);
      parentNode.appendChild(nextTextNode);
    }

    // 重置光标的位置
    const range = new Range();
    const selection: any = window.getSelection();

    range.setStart(nextTextNode, 0); // 光标放在 nextTextNode 的开头
    range.setEnd(nextTextNode, 0); // 确保光标在开头，而不是选中任何字符

    selection.removeAllRanges();
    selection.addRange(range);
  }
};


// 键盘抬起事件

const handleKeyUp = async () => {
  if (divRef.value.innerText.length > 200) {
    divRef.value.innerText = divRef.value.innerText.substr(0, 200);
    // 光标移动到最后
    document.execCommand('selectAll', false, '');
    let a: any = document.getSelection();
    a.collapseToEnd();
  }
  if (divRef.value.innerText.length <= 200 && showAt()) {
    const node1 = getRangeNode()
    const endIndex1 = getCursorIndex() //获取光标位置
    node.value = node1
    endIndex.value = endIndex1
    divRef.value.blur(); // 移除焦点
    mentionPopupRef.value.open()
  } else {
    mentionPopupRef.value.hide()
  }
  if (divRef.value.innerText.length > 1000) {

    divRef.value.innerText = divRef.value.innerText.substr(
        0,

        1000
    )

    // 光标移动到最后

    document.execCommand('selectAll', false, '')

    //将选种网页中的全部内容，也就是全部文本,第二个参数为true，会显示对话框

    let a: any = document.getSelection()

    a.collapseToEnd()

  }
  const cleanedText = divRef.value.innerText.replace(/[\u200B-\u200D\uFEFF]/g, '');
  const length = cleanedText.length;
  emit('changeText', length)
  // console.log('当前输入内容的长度:', length);
}

// 键盘按下事件
const handleKeyDown = (event) => {
  if (event.key === 'Backspace') {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const startContainer = range.startContainer;
      const startOffset = range.startOffset;

      // 如果光标不在文本的开始位置，则执行删除操作
      if (startOffset > 0) {
        range.setStart(startContainer, startOffset - 1);
        range.setEnd(startContainer, startOffset);
        range.deleteContents();
      } else {
        // 如果光标在文本的开始位置，且存在前一个节点，则将光标移动到前一个节点的末尾
        const previousSibling = startContainer.previousSibling;
        if (previousSibling) {
          range.setStart(previousSibling, previousSibling.textContent.length);
          range.setEnd(previousSibling, previousSibling.textContent.length);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      }
      event.preventDefault(); // 阻止默认的删除行为
    }
  }
};

// const handleMobileDelete = () => {
//   const selection = window.getSelection();
//   if (!selection.rangeCount) return;
//
//   const range = selection.getRangeAt(0);
//   const startContainer = range.startContainer;
//
//   // 检测是否在@标签附近
//   const prevNode = startContainer.previousSibling;
//   if (prevNode?.dataset?.deletable) {
//     prevNode.remove();
//     range.setStart(startContainer, 0);
//     range.collapse(true);
//     selection.removeAllRanges();
//     selection.addRange(range);
//   } else {
//     // 处理普通文本删除
//     if (range.startOffset > 0) {
//       range.setStart(startContainer, range.startOffset - 1);
//       range.setEnd(startContainer, range.startOffset);
//       range.deleteContents();
//     }
//   }
// };

const handleUser = (user1) => {
  console.log('user1', user1)
  userList.value.push(user1)
  replaceAtUser(user1)
  user.value = user1
  divRef.value.focus();
  const cleanedText = divRef.value.innerText.replace(/[\u200B-\u200D\uFEFF]/g, '');
  const length = cleanedText.length;
  emit('changeText', length)
}

</script>


<style scoped lang="less">

.content {

  width: 50%;

  font-family: sans-serif;


  h1 {

    text-align: center;

  }

}


.editor {
  width: 88vw;
  //height: 94px;
  //background: #fff;
  //border: 1px solid #ccc;
  font-size: 14px;
  border-radius: 5px;
  text-align: left;
  padding: 8px;
  overflow: auto;
  line-height: 1.4;

  &-focus {

    outline: none;

  }

  .tips {
    font-size: 14px;
    color: #999999;
    font-weight: 400;
  }
}
.at-user {
  color: blue; /* @用户文本颜色 */
  cursor: pointer; /* 鼠标悬停时显示指针 */
  /* 其他样式 */
}
</style>