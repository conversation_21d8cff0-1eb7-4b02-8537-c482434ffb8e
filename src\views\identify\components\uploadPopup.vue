<template>
  <van-popup
      v-if="popShow"
      v-model:show="changeOverlay"
      round
      position="bottom"
  >
    <div class="btn-container">
      <div class="type-btn" @click="handleUploadType('camera')">拍照</div>
      <div class="type-line"/>
      <div class="type-btn" @click="handleUploadType('album')">从相册中选择</div>
      <div class="division"/>
      <div class="type-btn" @click.stop="changeOverlay = false">取消</div>
    </div>
  </van-popup>

  <!-- 拍照用input -->
  <input
      ref="cameraInput"
      type="file"
      hidden
      accept="image/*"
      capture="environment"
      @change="handleFileSelect"
  >

  <!-- 相册用input -->
  <input
      ref="albumInput"
      type="file"
      hidden
      accept="image/*"
      :multiple="false"
      :capture="undefined"
      @change="handleFileSelect"
  >
</template>

<script setup>
import {ref, computed} from 'vue'
import {showLoadingToast, closeToast} from 'vant'
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();
const emit = defineEmits(['upload-success', 'upload-error'])
const cameraInput = ref(null)
const albumInput = ref(null)
const changeOverlay = ref(false)


const userInfo = computed(() => {
  return authStore.userInfo
})


const props = defineProps({
  popShow: {
    type: Boolean,
    default: true
  }
})

// 压缩配置
const compressConfig = {
  maxWidth: 1500,
  quality: 0.9,
  mimeType: 'image/jpeg'
}

const handleUploadType = (type) => {
  changeOverlay.value = false
  if (type === 'camera') {
    cameraInput.value.click()
  } else {
    albumInput.value.click()
  }
}

// const handleFileSelect = async (e) => {
//   const files = Array.from(e.target.files)
//   if (files.length === 0) return
//
//   showLoadingToast({message: '处理中...', forbidClick: true})
//
//   try {
//     const compressedFiles = await Promise.all(
//         files.map(file => compressImage(file)))
//     emit('upload-success', compressedFiles)
//   } catch (error) {
//     emit('upload-error', error)
//   } finally {
//     e.target.value = '' // 清空已选文件
//     closeToast()
//   }
// }
const handleFileSelect = async (e) => {
  const files = Array.from(e.target.files)
  if (files.length === 0) return

  showLoadingToast({message: '处理中...', forbidClick: true})

  try {
    const compressedFiles = await Promise.all(
        files.map(file => compressImage(file)))
    const formData = new FormData();
    formData.append("file", compressedFiles[0]);
    const res = await identifyApi.uploadFile(userInfo.value.id, formData);
    emit('upload-success', res.data)
  } catch (error) {
    emit('upload-error', error)
  } finally {
    e.target.value = '' // 清空已选文件
    closeToast()
  }
}

// 图片压缩方法（保持不变）
const compressImage = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const img = await new Promise((resolve, reject) => {
          const image = new Image()
          image.onload = () => resolve(image)
          image.onerror = reject
          image.src = e.target.result
        })

        const canvas = document.createElement('canvas')
        const ratio = Math.min(compressConfig.maxWidth / img.width, 1)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio

        canvas.getContext('2d').drawImage(img, 0, 0, canvas.width, canvas.height)

        canvas.toBlob((blob) => {
          // 检查压缩后图片大小
          const maxSize = 1 * 1024 * 1024; // 1MB
          if (blob.size > maxSize) {
            // reject(new Error('压缩后图片大小超过2MB限制'));
            showToast('图片大小超过1MB限制')
          } else {
            resolve(new File([blob], file.name, {
              type: compressConfig.mimeType,
              lastModified: Date.now()
            }));
          }
        }, compressConfig.mimeType, compressConfig.quality);
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

// 暴露方法
const open = () => {
  if(props.popShow) {
    changeOverlay.value = true
  } else {
    albumInput.value.click()
  }
}
const hide = () => changeOverlay.value = false
defineExpose({open, hide})
</script>

<style scoped lang="scss">
.btn-container {
  .type-btn {
    color: #333333;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    padding: 20px 0;
  }
  .type-line {
    margin: 0 20px;
    height: 1px;
    background-color: #F5F5F5;
  }
  .division {
    height: 5px;
    background-color: #F5F5F5;
  }
}
</style>