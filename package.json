{"name": "app-front-end-mobile-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.8.1", "echarts": "^6.0.0", "lodash": "^4.17.21", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "qs": "^6.14.0", "sass": "^1.85.1", "sass-loader": "^16.0.5", "vant": "^4.9.17", "vconsole": "^3.15.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.5", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.1", "postcss-pxtorem": "^6.1.0", "unplugin-auto-import": "^19.1.0", "unplugin-vue-components": "^28.4.0", "vite": "5.2.8"}}