<template>
  <div class="tab-bar-container" :class="{'ios-bar': IS_IOS}">
    <div v-for="item in tabBarList" class="tab-bar-box">
      <div class="bar-item" @click="jumpPage(item.id)">
        <img class="item-icon" :src="page === item.id ? item.icon2 : item.icon"/>
        <div class="item-text" :class="{'active-text': page === item.id}">{{ item.name }}</div>
        <div v-if="item.id == 4 && diffNew" class="circle-box"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref} from "vue";
import {isIOS} from "@/utils/common.js";
import tab1 from "@/assets/images/tabbar/tab1.png"
import tab2 from "@/assets/images/tabbar/tab2.png"
import tab3 from "@/assets/images/tabbar/tab3.png"
import tab4 from "@/assets/images/tabbar/tab4.png"
import tab11 from "@/assets/images/tabbar/tab11.png"
import tab22 from "@/assets/images/tabbar/tab22.png"
import tab33 from "@/assets/images/tabbar/tab33.png"
import tab44 from "@/assets/images/tabbar/tab44.png"
import {useAuthStore} from "@/stores/auth.js";
import {useRouter} from "vue-router";

const authStore = useAuthStore();
const router = useRouter()

const IS_IOS = isIOS()

const tabBarList = ref([
  {id: 1, name: '鉴定', icon: tab1, icon2: tab11},
  {id: 2, name: '榜单', icon: tab2, icon2: tab22},
  {id: 3, name: '数据', icon: tab3, icon2: tab33},
  {id: 4, name: '我的', icon: tab4, icon2: tab44},
])

const props = defineProps({
  page: {
    type: Number,
    default: 1
  },
  pageIndex: {
    type: Boolean,
    default: false
  },
  diffNew: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['myPage', 'backTop'])

const jumpPage = (id) => {
  if (id === 1) {
    emit('backTop')
  }
  if (props.page === id) return;
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    switch (id) {
      case 1:
        window.mag.newWin(`${apiUrl}/identify/index?mag_hide_progress=1`, {type: props.rankingType});
        break
      case 2:
        window.mag.newWin(`${apiUrl}/details/statistics?mag_hide_progress=1`, {type: props.rankingType});
        break
      case 3:
        window.mag.newWin(`${apiUrl}/dataStatistics/index?mag_hide_progress=1`, {type: props.rankingType});
        break
      case 4:
        if (props.pageIndex) {
          emit('myPage')
          return
        }
        window.mag.newWin(`${apiUrl}/myAppraisal/index?mag_hide_progress=1`);
        break
    }
  } else {
    switch (id) {
      case 1:
        router.push({
          name: 'identify',
        })
        break
      case 2:
        router.push({
          name: 'statistics',
          query: {
            type: props.rankingType
          }
        })
        break
      case 3:
        router.push({
          name: 'dataStatistics'
        })
        break
      case 4:
        if (props.pageIndex) {
          emit('myPage')
          return
        }
        router.push({
          name: 'myAppraisal'
        })
        break
    }
  }
  switch (id) {
    case 1:
      break
    case 2:
      break
    case 3:
      break
  }
}


</script>

<style lang="scss" scoped>
.tab-bar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 49px;
  padding: 6px 27px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  z-index: 999;

  .tab-bar-box {
    .bar-item {
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: column;

      .item-icon {
        width: 24px;
        height: 24px;
      }

      .item-text {
        margin-top: 4px;
        color: #9280AC;
        font-size: 10px;
        font-weight: 400;
      }

      .active-text {
        color: #9671F1;
        font-weight: 600;
      }
    }
  }

  .circle-box {
    position: absolute;
    top: -2px;
    right: -4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #FF3700;
  }
}

.ios-bar {
  padding: 6px 27px 20px;
}
</style>