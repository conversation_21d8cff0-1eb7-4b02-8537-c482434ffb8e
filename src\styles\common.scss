/* 外边距、内边距全局样式 */
@for $i from 0 through 200 {
  .margin-#{$i} {
    margin: #{$i}px !important;
  }
  .mt-#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr-#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb-#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml-#{$i} {
    margin-left: #{$i}px !important;
  }
  .mtb-#{$i} {
    margin-top: #{$i}px !important;
    margin-bottom: #{$i}px !important;
  }
  .mlr-#{$i} {
    margin-left: #{$i}px !important;
    margin-right: #{$i}px !important;
  }

  .padding-#{$i} {
    padding: #{$i}px !important;
  }
  .pt-#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr-#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb-#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl-#{$i} {
    padding-left: #{$i}px !important;
  }
  .ptb-#{$i} {
    padding-top: #{$i}px !important;
    padding-bottom: #{$i}px !important;
  }
  .plr-#{$i} {
    padding-left: #{$i}px !important;
    padding-right: #{$i}px !important;
  }
}

// ==============================

.w-full {
  width: 100%;
}

.w-full-v {
  width: 100vw;
}

.h-full {
  height: 100%;
}

.h-full-v {
  height: 100vh;
}

.w-h-full {
  width: 100%;
  height: 100%;
}

.w-h-full-v {
  width: 100vw;
  height: 100vh;
}

.aspect-1 {
  aspect-ratio: 1;
}

.cursor-pointer {
  cursor: pointer;

  &:active {
    opacity: 0.9;
    transform: translate(0.4px, 0.4px);
  }
}

@for $i from 0 through 12 {
  .line-height-#{$i} {
    line-height: #{$i};
  }
}

.text-center {
  text-align: center;
}

.text-Abc {
  text-transform: Capitalize;
}

.text-ABC {
  text-transform: Uppercase;
}

.text-abc {
  text-transform: Lowercase;
}

.text-delete-line {
  text-decoration: line-through;
}

.text-under-line {
  text-decoration: underline;
}

.text-bold {
  font-weight: bold;
}

.text-italic {
  font-style: italic;
}

.text-select-none {
  user-select: none;
}

.vertical-middle {
  vertical-align: middle;
}

/*  -- flex弹性布局 -- */
.flex {
  display: flex !important;
  flex-direction: row !important;
}

.flex-col {
  display: flex !important;
  flex-direction: column !important;
}

.flex-vhc {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.flex-hc {
  display: flex !important;
  justify-content: center !important;
}

.flex-vc {
  display: flex !important;
  align-items: center !important;
}

.flex-col-vhc {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.flex-col-hc {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

.flex-col-vc {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
}

@for $i from 0 through 1 {
  .flex-shrink-#{$i} {
    flex-shrink: #{$i} !important;
  }
}

@for $i from 0 through 24 {
  .flex-grow-#{$i} {
    flex-grow: #{$i};
  }

  .flex-#{$i} {
    flex: #{$i} 0 0%;
  }
}

@for $i from 0 through 60 {
  .gap-#{$i} {
    gap: #{$i}px;
  }
}

.basis-xs {
  flex-basis: 20%;
}

.basis-sm {
  flex-basis: 40%;
}

.basis-df {
  flex-basis: 50%;
}

.basis-lg {
  flex-basis: 60%;
}

.basis-xl {
  flex-basis: 80%;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-center {
  align-self: flex-center;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.align-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

/* 宫格布局 */
.grid {
  display: grid !important;
}

@for $i from 0 through 24 {
  .grid.grid-col-#{$i} {
    grid-template-columns: repeat(#{$i}, 1fr);
  }
}

.grid.grid-gap-sm {
  grid-gap: 10px;
}

.grid.grid-gap-df {
  grid-gap: 16px;
}

.grid.grid-gap-lg {
  grid-gap: 30px;
}

.grid.grid-gap-xl {
  grid-gap: 40px;
}

.grid.grid-gap-col-sm {
  grid-column-gap: 10px;
}

.grid.grid-gap-col-df {
  grid-column-gap: 20px;
}

.grid.grid-gap-col-lg {
  grid-column-gap: 30px;
}

.grid.grid-gap-col-xl {
  grid-column-gap: 40px;
}

.grid.grid-gap-row-sm {
  grid-row-gap: 10px;
}

.grid.grid-gap-row-df {
  grid-row-gap: 20px;
}

.grid.grid-gap-row-lg {
  grid-row-gap: 30px;
}

.grid.grid-gap-row-xl {
  grid-row-gap: 40px;
}

// 宫格格子
.grid .grid-item-sm {
  height: 40px;
}

.grid .grid-item-df {
  height: 60px;
}

.grid .grid-item-lg {
  height: 80px;
}

.grid .grid-item-xl {
  height: 100px;
}

// 字行限制
@for $i from 0 through 4 {
  .text-ellipsis-#{$i} {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: #{$i};
    line-clamp: #{$i};
  }
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.inline-block {
  display: inline-block;
}

.text-xs {
  font-size: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-df {
  font-size: 16px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 20px;
}

.text-xxl {
  font-size: 30px;
}

.text-sl {
  font-size: 40px;
}

.text-xsl {
  font-size: 60px;
}

.text-xxsl {
  font-size: 80px;
}

.color-primary {
  color: var(--el-color-primary);
}

.color-success {
  color: var(--el-color-success);
}

.color-info {
  color: var(--el-color-info);
}

.color-warning {
  color: var(--el-color-warning);
}

.color-danger {
  color: var(--el-color-danger);
}

.color-red {
  color: #e54d42;
}

.color-orange {
  color: #f37b1d;
}

.color-yellow {
  color: #fbbd08;
}

.color-olive {
  color: #8dc63f;
}

.color-green {
  color: #39b54a;
}

.color-cyan {
  color: #1cbbb4;
}

.color-blue {
  color: #0081ff;
}

.color-bili {
  color: #fb7299;
}

.color-tyblue {
  color: #66ccff;
}

.color-miku {
  color: #39c5bb;
}

.color-purple {
  color: #6739b6;
}

.color-mauve {
  color: #9c26b0;
}

.color-pink {
  color: #e03997;
}

.color-brown {
  color: #a5673f;
}

.color-grey {
  color: #8799a3;
}

.color-gray {
  color: #aaaaaa;
}

.color-dark {
  color: #333333;
}

.color-black {
  color: #000000;
}

.color-white {
  color: #ffffff;
}

.color-inherit {
  color: inherit;
}

.bg-red {
  background-color: #e54d42;
  color: #ffffff;
}

.bg-orange {
  background-color: #f37b1d;
  color: #ffffff;
}

.bg-yellow {
  background-color: #fbbd08;
  color: #333333;
}

.bg-olive {
  background-color: #8dc63f;
  color: #ffffff;
}

.bg-green {
  background-color: #39b54a;
  color: #ffffff;
}

.bg-cyan {
  background-color: #1cbbb4;
  color: #ffffff;
}

.bg-blue {
  background-color: #0081ff;
  color: #ffffff;
}

.bg-purple {
  background-color: #6739b6;
  color: #ffffff;
}

.bg-mauve {
  background-color: #9c26b0;
  color: #ffffff;
}

.bg-pink {
  background-color: #e03997;
  color: #ffffff;
}

.bg-brown {
  background-color: #a5673f;
  color: #ffffff;
}

.bg-grey {
  background-color: #8799a3;
  color: #ffffff;
}

.bg-gray {
  background-color: #f0f0f0;
  color: #333333;
}

.bg-dark {
  background-color: #333333;
  color: #ffffff;
}

.bg-black {
  background-color: #000000;
  color: #ffffff;
}

.bg-white {
  background-color: #ffffff;
  color: #333333;
}

// 旋转度
@for $i from 0 through 180 {
  // x轴旋转
  .rotate-x-#{$i} {
    transform: rotateX(#{$i}deg);
  }
  // y轴旋转
  .rotate-y-#{$i} {
    transform: rotateY(#{$i}deg);
  }
  // 普通旋转
  .rotate-#{$i} {
    transform: rotate(#{$i}deg);
  }
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.shield-box {
  position: relative;
  margin-left: 6px;
  display: flex;
  align-items: center;
  color: #FA5151;
  font-size: 10px;
  border-radius: 9px;

  .shield-icon {
    width: 13px;
    height: 14px;
  }

  .shield-text {
    padding: 2px 4px 2px 4px;
  }
}

.shield-box-active {
  background-color: #FFEDED;
}

.grade-box {
  position: relative;
  margin-left: 5px;
  display: flex;
  align-items: center;
  color: #3D3D3D;
  font-weight: 700;
  font-size: 11px;
  border-radius: 9px;

  .grade-icon {
    position: absolute;
    left: -10%;
    width: 18px;
    height: 18px;
  }

  .grade-text {
    padding: 2px 6px 2px 18px;
  }
}

.grade-box-red {
  background-color: #FFEDED;
}
.grade-box-blue {
  background-color: #DDF4FD;
}
.grade-box-yellow {
  background-color: #FFF3D5;
}
.grade-box-purple {
  background-color: #F3EEFF;
}

//推荐标签
.title-tag {
  margin-right: 5px;
  flex-shrink: 0;
  color: #FA5151;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 3px;
  border-radius: 3px;
  border: 1px solid #FA5151;
}
//左上角异鉴率标识
.image-top {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  color: #FFFFFF;
  font-size: 10px;
  font-weight: 700;

  .top-people {
    display: flex;
    align-items: center;
    padding-right: 2px;
    border-radius: 5px 0 5px 0;
  }

  .top-ratio {
    display: flex;
    align-items: center;
    padding: 4px;
    margin-left: 1px;
    border-radius: 0 0 5px 5px;
  }

  .per-text {
    font-size: 8px;
    font-weight: 400;
  }

  .jd-icon {
    width: 9px;
    height: 10px;
    padding: 4px;
  }
}