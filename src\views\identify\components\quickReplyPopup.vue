<template>
  <van-popup
      v-if="replyShow"
      v-model:show="replyShow"
      round
      position="bottom"
  >
    <div class="release-popup">
      <div class="title-box">
        <div></div>
        <div>快捷回复</div>
        <img class="close-icon" :src="closeIcon" @click="hide"/>
      </div>
      <div v-if="replyList.length" class="list-container mt-20">
        <div class="list-box" v-for="item in replyList">
          <van-swipe-cell>
            <div class="item-box">
              <div class="item-name">{{ item.name }}</div>
              <div class="item-right">
                <img class="edit-icon" :src="editIcon" @click="editReply(item)"/>
                <div class="use-btn" @click="handleUse(item)">使用</div>
              </div>
            </div>
            <template #right>
              <div class="del-btn" @click="delReply(item)">删除</div>
            </template>
          </van-swipe-cell>
          <div class="list-line"></div>
        </div>
      </div>
      <Empty v-if="!replyList.length" class="mt-82  mb-100"/>
      <div class="footer-box" v-if="replyList.length < 8">
        <div class="footer-btn" @click="addReply">添加快捷回复</div>
      </div>
      <QuickDialog
          style="z-index: 99999"
          ref="editDialogRef"
          :title="dialogTitle"
          btnLeftTxt="取消"
          btnRightTxt="保存"
          @rightClick="confirmEditHide"
      >
        <template #default>
          <div class="input-box">
            <van-field v-model="replyContent" type="textarea" placeholder="请输入..."
                       maxlength="20" :formatter="formatter"/>
          </div>
        </template>
      </QuickDialog>
    </div>
  </van-popup>
</template>

<script setup>
import {ref} from "vue";
import closeIcon from '@/assets/images/common/closeIcon.png'
import editIcon from "@/assets/images/details/editIcon.png"
import QuickDialog from "@/views/identify/details/components/quickDialog.vue"
import {getCache, setCache} from "@/utils/cache.js";


const replyShow = ref(false)
const editDialogRef = ref(false)
const replyContent = ref('')
const currentReplyId = ref('')
const dialogTitle = ref('')


const props = defineProps({
  content: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['use-reply'])

const formatter = (value) => value.replace(/[\r\n]/g, '')

function generateSimpleID() {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

const replyList = ref([])

const initList = ref([
  {id: generateSimpleID(), name: '请补清晰T型接头近照'},
  {id: generateSimpleID(), name: '请补清晰中杆钢号近照'},
  {id: generateSimpleID(), name: '请补清晰锥盖规格近照'},
])


const editReply = (item) => {
  dialogTitle.value = '编辑快捷回复内容'
  currentReplyId.value = item.id
  replyContent.value = item.name
  editDialogRef.value.show()
}

const addReply = () => {
  dialogTitle.value = '添加快捷回复内容'
  replyContent.value = ''
  editDialogRef.value.show()
}

const handleUse = (item) => {
  if(props.content.includes(item.name)) {
    showToast('该内容已在输入框中')
    return
  }
  let recentList = getCache('OFTEN_REPLY_MAP')
      ? JSON.parse(getCache('OFTEN_REPLY_MAP'))
      : [];
  const isFirstItem = recentList.length > 0 && recentList[0].id === item.id;
  if (!isFirstItem) {
    recentList = recentList.filter(reply => reply.id !== item.id);
    recentList.unshift({
      id: item.id,
      name: item.name
    });
  }
  if (recentList.length > 2) {
    recentList = recentList.slice(0, 2);
  }
  setCache('OFTEN_REPLY_MAP', JSON.stringify(recentList));
  emit('use-reply', item);
  hide();
};


const delReply = (val) => {
 let list = replyList.value.filter(item => item.name !== val.name);
  replyList.value = list
  setCache('QUICK_REPLY_MAP', JSON.stringify(list))
}


const confirmEditHide = () => {
  if(!replyContent.value || replyContent.value.trim() === '') {
    showToast('请填写回复内容')
    return
  }
  if(currentReplyId.value) {
    //编辑
    replyList.value.forEach((item) => {
      if(currentReplyId.value === item.id) {
        item.name = replyContent.value
      }
    })
  } else {
    //新增
    replyList.value.push({
      id: generateSimpleID(),
      name: replyContent.value
    })
  }
  editDialogRef.value.hide()
  setCache('QUICK_REPLY_MAP', JSON.stringify(replyList.value))
  currentReplyId.value = ''
}


const show = () => {
  if(!getCache('QUICK_REPLY_MAP')) {
    replyList.value = initList.value
    setCache('QUICK_REPLY_MAP', JSON.stringify(initList.value))
  } else {
    replyList.value = JSON.parse(getCache('QUICK_REPLY_MAP'))
  }
  replyShow.value = true
}

const hide = () => {
  replyShow.value = false
}


defineExpose({show, hide, handleUse})

</script>

<style scoped lang="scss">
.release-popup {
  margin: 15px 12px 20px;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .list-container {
    min-height: 300px;
    font-size: 16px;

    .list-box {
    }

    .list-line {
      margin: 15px 0;
      height: 1px;
      background-color: #F5F5F5;
    }

    .item-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item-name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        min-width: 0;
      }
      .item-right {
        margin-left: 10px;
        flex-shrink: 0;
        display: flex;
        align-items: center;

        .edit-icon {
          padding-bottom: 2px;
          width: 20px;
          height: 20px;
          margin-right: 12px;
        }

        .use-btn {
          color: #478E87;
          font-size: 14px;
          font-weight: 600;
          padding: 7px 15px;
          background-color: #DFF7F6;
          border: 1px solid #93C3BF;
          border-radius: 15px;
        }
      }
    }

    .del-btn {
      flex-shrink: 0;
      color: #FFFFFF;
      font-size: 14px;
      margin-left: 5px;
      border-radius: 15px;
      padding: 8px 15px;
      background-color: #FA5151;
    }
  }

  .footer-box {
    .footer-btn {
      font-size: 16px;
      color: #FFFFFF;
      height: 44px;
      line-height: 44px;
      text-align: center;
      border-radius: 22px;
      background-color: #478E87;
    }
  }

  .input-box {
    height: 60px;
    border-radius: 8px;
    border: 1px solid #D8D8D8;
  }
}
.van-cell {
  padding: 10px;
  background-color: transparent;
}
</style>