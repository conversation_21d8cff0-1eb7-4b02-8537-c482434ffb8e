<!DOCTYPE html>
<html lang="">
<head>
    <meta charset="UTF-8">
    <!--    <link rel="icon" href="/favicon.ico">-->
    <link rel="icon" href="https://www.badmintoncn.com/favicon.ico">
    <!--      ?version=1.0.0-->
    <script>
        (function () {
            let el = document.createElement('script')
            el.src = "https://app.badmintoncn.com/public/static/dest/js/libs/magjs-x.js?version=5.6.0"
            document.head.appendChild(el)
        })()
    </script>
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0,user-scalable=no, maximum-scale=1.0, minimum-scale=1.0,viewport-fit=cover"/>
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="expires" content="0">
    <meta http-equiv="Cache" content="no-cache">
    <!--      <title>Vite App</title>-->
    <title id="dynamic-title"></title>
</head>
<body>
<div id="app"></div>
<script type="module" src="/src/main.js"></script>
<script>
    function setTitle(newTitle) {
        const titleElement = document.getElementById('dynamic-title');
        const oldTitle = titleElement.textContent;
        titleElement.style.opacity = 0;
        setTimeout(() => {
            titleElement.textContent = newTitle;
            titleElement.style.opacity = 1;
        }, 300);
        if (window.gtag) {
            window.gtag('event', 'title_change', {
                'value': newTitle,
                'previous_title': oldTitle
            });
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        setTitle('');
    });
</script>
</body>
</html>
