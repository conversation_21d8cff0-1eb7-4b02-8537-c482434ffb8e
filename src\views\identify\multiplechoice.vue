<template>
  <div class="screen-page">
    <div class="header-container">
      <van-search v-model="searchText" placeholder="搜索求鉴关键词…" shape="round">
        <template #left-icon>
          <img class="search-icon" :src="searchIcon">
        </template>
      </van-search>
    </div>

    <div class="classify">
      <TitleTop title="所属品类" :right="false"/>
    </div>
    <div class="tag-container">
      <div
          v-for="(item, index) in categoryList"
          :key="index"
          class="tag-item"
          :class="{'active-item': isActive('category', item.name)}"
          @click="toggleSelection('category', item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="classify mt-10">
      <TitleTop title="求鉴状态" :right="false"/>
    </div>
    <div class="tag-container">
      <div
          v-for="(item, index) in stateList"
          :key="index"
          class="tag-item"
          :class="{'active-item': isActive('state', item.name)}"
          @click="toggleSelection('state', item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="classify mt-10">
      <TitleTop title="所属品牌" :right="false"/>
    </div>
    <div class="tag-container">
      <div
          v-for="(item, index) in brandList"
          :key="index"
          class="tag-item"
          :class="{'active-item': isActive('brand', item.name)}"
          @click="toggleSelection('brand', item)"
      >
        <div>{{ item.name }}</div>
        <div class="pt-4">{{ item.count }}</div>
      </div>
    </div>
    <div class="footer-box">
      <div class="btn left-btn" @click="handleCancel">{{`重置（${totalSelectedTags}）`}}</div>
      <div class="btn right-btn" @click="handleSave">应用</div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed } from "vue";
import TitleTop from "@/components/titleTop.vue";
import searchIcon from "@/assets/images/identify/searchIcon.png";
import {useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();

const searchText = ref('');
const tags = ref({
  category: ['全部'],
  state: ['全部'],
  brand: ['全部']
});

const categoryList = ref([
  {name: '全部'},
  {name: '羽毛球拍'},
  {name: '羽毛球鞋'},
  {name: '羽毛球'},
  {name: '其他'},
]);

const stateList = ref([
  {name: '全部'},
  {name: '待鉴定'},
  {name: '已鉴定'},
]);

const brandList = ref([
  {name: '全部', count: 999999},
  {name: '尤尼克斯', count: 9999},
  {name: '李宁', count: 99999},
  {name: '威克多', count: 990},
  {name: '其他', count: 999},
]);

function isActive(type, name) {
  return tags.value[type].includes(name);
}

function toggleSelection(type, item) {
  const currentTags = tags.value[type];
  let prevCount = currentTags.length;

  if (item.name === '全部') {
    tags.value[type] = ['全部'];
  } else {
    if (currentTags.includes('全部')) {
      tags.value[type] = [item.name];
    } else {
      if (currentTags.includes(item.name)) {
        tags.value[type] = currentTags.filter(name => name !== item.name);
      } else {
        tags.value[type].push(item.name);
      }
    }
    if (tags.value[type].length === 0) {
      tags.value[type] = ['全部'];
    }
  }
}

const totalSelectedTags = computed(() => {
  let count = 0;
  for (const type in tags.value) {
    count += tags.value[type].length;
    if (tags.value[type].includes('全部')) {
      count -= (tags.value[type].length - 1);
    }
  }
  return count;
});

const handleCancel = () => {

}


const handleSave = () => {

}

if(authStore.phone) {
  window.mag.setTitle('筛选');
} else {
  document.title = '筛选'
}
window.mag.showNavigation();
</script>

<style scoped lang="scss">
.screen-page {
  margin: 12px;
  height: 100vh;
  position: relative;

  .header-container {
    .van-search {
      padding: 0;
    }

    .search-icon {
      height: 14px;
      width: 14px;
    }
  }

  .classify {
    padding: 12px 0;
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px;

    .tag-item {
      width: calc((100% - 40px) / 4);
      color: #3D3D3D;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      padding: 10px 0;
      border-radius: 5px;
      background-color: #F5F5F5;
      cursor: pointer;
    }

    .active-item {
      color: #478E87;
      background-color: #DFF7F6;
    }
  }
  .footer-box {
    position: absolute;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .btn {
      width: 50%;
      font-size: 16px;
      text-align: center;
    }
    .left-btn {
      padding: 11px;
      color: #478E87;
      border-radius: 22px;
      background-color: #DFF7F6;
      margin-right: 15px;
    }
    .right-btn {
      padding: 11px 0;
      color: #FFFFFF;
      border-radius: 22px;
      background-color: #478E87;
    }
  }
}
</style>