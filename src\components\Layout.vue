<template>
  <div class="page-wrapper">
    <div class="page-container">
      <div class="page-header" v-if="$slots.header">
        <slot name="header" />
      </div>
      <div class="page-body">
        <slot />
      </div>
      <div class="page-footer" v-if="$slots.footer">
        <slot name="footer" />
      </div>
    </div>
    <div class="page-modal-container" v-if="$slots.modal">
      <slot name="modal" />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  enablePullDownRefresh: {
    type: <PERSON><PERSON>an,
    default: false
  }
});
</script>

<style lang="scss">
.page-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  .page-header,
  .page-footer {
    flex-shrink: 0;
  }
  .page-body {
    width: 100%;
    flex: 1;
    overflow: auto;
  }
  .page-modal-container {
    height: 0;
  }
  .page-header {
    position: relative;
    // position: sticky;
    // top: 0;
    width: 100%;
    // z-index: 9;
    z-index: 20;
  }
  .page-footer {
    // position: fixed;
    // right: 0;
    // bottom: 0;
    width: 100%;
    // z-index: 20;
  }
}
</style>
