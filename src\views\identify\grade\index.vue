<template>
  <div class="grade-page">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      :style="{ height: `calc(100vh - 10px - ${titleHeight}px)`,paddingTop: `${titleHeight}px`}">
      <div class="grade-bg-box">
        <div class="user-box">
          <div class="user-content">
            <div class="user-mes">
              <img class="avatar-icon" :src="userMes?.user?.avatar"/>
              <img class="grade-icon" :src="wheatIcon"/>
              <div class="grade-text">{{ currentLevel }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="table-box pb-140">
        <van-row class="heading">
          <van-col class="heading-item" span="8">鉴定等级</van-col>
          <van-col class="heading-item item-border" span="8">等级图标</van-col>
          <van-col class="heading-item" span="8">经验范围</van-col>
        </van-row>
        <van-row class="table-body">
          <template v-for="(item, index) in tableList" :key="index">
            <van-col v-if="item.grade !== '暂无等级'" class="table-body-item"
                     :class="{ 'highlight': isInRange(item.range) }" span="8">
              <span class="item-text">{{ item.grade }}</span>
            </van-col>
            <!--            <van-col v-if="item.grade !== '暂无等级'" class="table-body-item item-border"-->
            <!--                     :class="{ 'highlight': isInRange(item.range) }" span="8">-->
            <!--              <template v-for="items in item.iconNumber">-->
            <!--                <img class="item-icon" :src="item.icon"/>-->
            <!--              </template>-->
            <!--            </van-col>-->
            <van-col v-if="item.grade !== '暂无等级'" class="table-body-item item-border"
                     :class="{ 'highlight': isInRange(item.range) }" span="8">
              <template v-for="val in 5" :key="val">
                <img class="item-icon" :src="val <= item.activeIcons ? item.icon : iconMap[getIconType(item.iconPath)].inactive"/>
              </template>
            </van-col>
            <van-col v-if="item.grade !== '暂无等级'" class="table-body-item"
                     :class="{ 'highlight': isInRange(item.range) }" span="8">
              <span class="item-text">{{ item.range }}</span>
            </van-col>
          </template>
        </van-row>
      </div>
    </van-pull-refresh>
    <div class="footer-box" @click="handleGrade">
      <div class="footer-top">
        <div class="top-left">
          <GradeTag :gradePage="true" :group-id="userMes?.user?.groupId" :uid="userMes?.user?.uid"
                    :userLevel="userMes?.user?.userLevel"/>
          <span class="ml-5">{{ `等级值 ${userMes?.user?.points}` }}</span>
        </div>
        <div class="top-right">
          {{ nextLevel === 'max' ? '您已达到最高等级' : `差${experienceToNextLevel}分升${nextLevel}` }}
        </div>
      </div>
      <div class="progress-box">
        <van-progress :color="currentColor" :track-color="currentProgressColor" :percentage="progress" :stroke-width="6"
                      :show-pivot="false"/>
      </div>
      <div class="footer-btn">提升等级</div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, computed} from "vue";
import GradeTag from "@/views/identify/grade/components/gradeTag.vue";
import wheatIcon from "@/assets/images/common/wheatIcon.png"
import loveIcon from "@/assets/images/common/loveIcon.png";
import jewelIcon from "@/assets/images/common/jewelIcon.png";
import goldenShield from "@/assets/images/common/goldenShield.png";
import crownIcon from "@/assets/images/common/crownIcon.png";
import loveHalfIcon from "@/assets/images/common/loveHalfIcon.png";
import jewelHalfIcon from "@/assets/images/common/jewelHalfIcon.png";
import goldenShieldHalf from "@/assets/images/common/goldenShieldHalf.png";
import crownHalfIcon from "@/assets/images/common/crownHalfIcon.png";
import loveGreyIcon from "@/assets/images/common/loveGreyIcon.png";
import jewelGreyIcon from "@/assets/images/common/jewelGreyIcon.png";
import goldenShieldGrey from "@/assets/images/common/goldenShieldGrey.png";
import crownGreyIcon from "@/assets/images/common/crownGreyIcon.png";
import {useAuthStore} from "@/stores/auth.js";
import {setCache} from "@/utils/cache.js";
import router from "@/router/index.js";
import identifyApi from "@/services/identify.js";

const authStore = useAuthStore();


const userInfo = computed(() => {
  return authStore.userInfo
})

const refreshing = ref(false)
const userMes = ref()
const userGrade = ref()
const nextLevel = ref('')
const currentLevel = ref('')
const experienceToNextLevel = ref(0)
const progress = ref()
const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;

const levelColors = {
  'a': '#FA5151',
  'a-0': '#A8A8A8',
  'b': '#3EA1FF',
  'c': '#FFBE00',
  'd': '#B073FF',
};

//表头和下面按钮的背景色
const headColors = {
  'a': '#FAE0E0',
  'a-0': '#EAEAEA',
  'b': '#CEE7FF',
  'c': '#FFEBB1',
  'd': '#F2DFFF',
}

//进度条和选中的条
const progressColors = {
  'a': '#FFF1F1',
  'a-0': '#F5F5F5',
  'b': '#EBF5FF',
  'c': '#FFF4D5',
  'd': '#F2DFFF',
}


const currentHeadColor = computed(() => {
  const level = userMes.value?.user?.userLevel;
  if (!level) return '';
  if (level.startsWith('a-0')) {
    return headColors['a-0'] || '';
  }
  const levelLetter = level[0].toLowerCase();
  return headColors[levelLetter] || '';
});

const currentProgressColor = computed(() => {
  const level = userMes.value?.user?.userLevel;
  if (!level) return '';
  if (level.startsWith('a-0')) {
    return progressColors['a-0'] || '';
  }
  const levelLetter = level[0].toLowerCase();
  return progressColors[levelLetter] || '';
});

const currentColor = computed(() => {
  const level = userMes.value?.user?.userLevel;
  if (!level) return '';
  if (level.startsWith('a-0')) {
    return levelColors['a-0'] || '';
  } else {

  }
  const levelLetter = level[0].toLowerCase();
  return levelColors[levelLetter] || '';
});

const iconMap = {
  love: {
    active: loveIcon,
    inactive: loveGreyIcon
  },
  jewel: {
    active: jewelIcon,
    inactive: loveIcon
  },
  goldenShield: {
    active: goldenShield,
    inactive: jewelIcon
  },
  crown: {
    active: crownIcon,
    inactive: goldenShield
  }
}

const getIconType = (iconPath) => {
  if (iconPath === 'loveIcon') return 'love';
  if (iconPath === 'jewelIcon') return 'jewel';
  if (iconPath === 'goldenShield') return 'goldenShield';
  if (iconPath === 'crownIcon') return 'crown';
  return 'love';
}

const tableList = ref([
  {grade: '暂无等级', icon: loveIcon, iconPath: 'loveIcon', range: '0～4'},
  {grade: '热心一级', icon: loveIcon, iconPath: 'loveIcon', activeIcons: 1, inactiveIcons: 4, range: '5～50'},
  {grade: '热心二级', icon: loveIcon, iconPath: 'loveIcon', activeIcons: 2, inactiveIcons: 3, range: '51～100'},
  {grade: '热心三级', icon: loveIcon, iconPath: 'loveIcon', activeIcons: 3, inactiveIcons: 2, range: '101～300'},
  {grade: '热心四级', icon: loveIcon, iconPath: 'loveIcon', activeIcons: 4, inactiveIcons: 1, range: '301～600'},
  {grade: '热心五级', icon: loveIcon, iconPath: 'loveIcon', activeIcons: 5, inactiveIcons: 0, range: '601～1200'},
  {grade: '蓝钻一级', icon: jewelIcon, iconPath: 'jewelIcon', activeIcons: 1, inactiveIcons: 4, range: '1201～2500'},
  {grade: '蓝钻二级', icon: jewelIcon, iconPath: 'jewelIcon', activeIcons: 2, inactiveIcons: 3, range: '2501～5000'},
  {grade: '蓝钻三级', icon: jewelIcon, iconPath: 'jewelIcon', activeIcons: 3, inactiveIcons: 2, range: '5001～9000'},
  {grade: '蓝钻四级', icon: jewelIcon, iconPath: 'jewelIcon', activeIcons: 4, inactiveIcons: 1, range: '9001～14000'},
  {grade: '蓝钻五级', icon: jewelIcon, iconPath: 'jewelIcon', activeIcons: 5, inactiveIcons: 0, range: '14001～20000'},
  {grade: '金盾一级', icon: goldenShield, iconPath: 'goldenShield', activeIcons: 1, inactiveIcons: 4, range: '20001～27000'},
  {grade: '金盾二级', icon: goldenShield, iconPath: 'goldenShield', activeIcons: 2, inactiveIcons: 3, range: '27001～35000'},
  {grade: '金盾三级', icon: goldenShield, iconPath: 'goldenShield', activeIcons: 3, inactiveIcons: 2, range: '35001～44000'},
  {grade: '金盾四级', icon: goldenShield, iconPath: 'goldenShield', activeIcons: 4, inactiveIcons: 1, range: '44001～54000'},
  {grade: '金盾五级', icon: goldenShield, iconPath: 'goldenShield', activeIcons: 5, inactiveIcons: 0, range: '54001～66000'},
  {grade: '紫冠一级', icon: crownIcon, iconPath: 'crownIcon', activeIcons: 1, inactiveIcons: 4, range: '66001～80000'},
  {grade: '紫冠二级', icon: crownIcon, iconPath: 'crownIcon', activeIcons: 2, inactiveIcons: 3, range: '80001～100000'},
  {grade: '紫冠三级', icon: crownIcon, iconPath: 'crownIcon', activeIcons: 3, inactiveIcons: 2, range: '100001～120000'},
  {grade: '紫冠四级', icon: crownIcon, iconPath: 'crownIcon', activeIcons: 4, inactiveIcons: 1, range: '120001～150000'},
  {grade: '紫冠五级', icon: crownIcon, iconPath: 'crownIcon', activeIcons: 5, inactiveIcons: 0, range: '150001～♾️'},
])

const isInRange = (range) => {
  const points = userMes.value?.user?.points;
  if (!points) return false;

  const [min, max] = range.split('～').map(str => {
    if (str === '♾️') return Infinity;
    return parseInt(str, 10);
  });

  return points >= min && points <= max;
};

const getExperienceToNextLevel = (currentExperience) => {
  let currentLevel = null;
  let nextLevel = null;

  for (let i = 0; i < tableList.value.length; i++) {
    const level = tableList.value[i];
    const [min, max] = level.range.split('～').map(val => val === '♾️' ? Infinity : parseInt(val, 10));

    if (currentExperience >= min && currentExperience <= max) {
      currentLevel = level;
      nextLevel = tableList.value[i + 1];
      break;
    }
  }

  if (!currentLevel) {
    return {experienceToNextLevel: 'max'};
  }

  if (!nextLevel) {
    return {experienceToNextLevel: 'max'};
  }

  const [currentMin, currentMax] = currentLevel.range.split('～').map(val => val === '♾️' ? Infinity : parseInt(val, 10));
  const [nextMin, nextMax] = nextLevel.range.split('～').map(val => val === '♾️' ? Infinity : parseInt(val, 10));

  const experienceToNextLevel = nextMin - currentExperience;

  // 计算当前分值占当前等级的百分比
  const currentLevelRange = currentMax - currentMin;
  const currentExperienceInLevel = currentExperience - currentMin;
  const percentageInCurrentLevel = (currentExperienceInLevel / currentLevelRange) * 100;

  return {
    currentLevel: currentLevel.grade,
    nextLevel: nextLevel.grade,
    experienceToNextLevel: experienceToNextLevel,
    percentageInCurrentLevel: percentageInCurrentLevel.toFixed(2)
  };
}


const onRefresh = async () => {
  await getUserMes()
  refreshing.value = false
}

const handleGrade = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/identify/index?mag_hide_progress=1`);
  } else {
    router.push({
      name: 'identify'
    })
  }
}

const getUserMes = async () => {
  let res = await identifyApi.queryUserList({ids: userInfo.value.id})
  if (res.code === 200) {
    let list = res.data?.list[0]
    userMes.value = list || {}
    const result = getExperienceToNextLevel(Number(userMes.value?.user?.points));
    nextLevel.value = result.nextLevel
    currentLevel.value = result.currentLevel
    experienceToNextLevel.value = result.experienceToNextLevel
    progress.value = result.percentageInCurrentLevel
    userGrade.value = userMes.value?.user?.userLevel.charAt(0)
    setCache('USER_MESSAGE', JSON.stringify(list))
    let bgColor = currentColor.value
    if (authStore.phone) {
      window.mag.setNavigationColor(bgColor);
      window.mag.hideMore();
      window.mag.setTitle('当前等级');
    } else {
      document.title = '当前等级'
    }
  }
}


onMounted(() => {
  getUserMes()
})

</script>

<style lang="scss" scoped>
.grade-page {
  //overscroll-behavior-y: auto !important;
  .content-table-list {
    overflow: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .grade-bg-box {
    position: relative;
    width: 100vw;
    height: 120px;
    //background-color: #B073FF;
    background-color: v-bind(currentColor);
    //background-color: #B073FF;

    .user-box {
      position: absolute;
      bottom: 14px;
      left: 50%;
      transform: translateX(-50%);

      .user-content {
        position: relative;

        .grade-icon {
          position: absolute;
          width: 151px;
          height: 77px;
          margin-top: 19px;
        }

        .user-mes {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .avatar-icon {
          width: 68px;
          height: 68px;
          border-radius: 50%;
          border: 3px solid #FFFFFF;
        }

        .grade-text {
          margin-top: 4px;
          color: #3D3D3D;
          font-size: 13px;
          font-weight: 700;
          padding: 3px 5px;
          border-radius: 5px;
          background-color: #FFFFFF;
        }
      }
    }
  }

  .table-box {
    font-size: 11px;
    color: #3D3D3D;

    .heading {
      font-size: 12px;
      font-weight: 600;
      background-color: v-bind(currentHeadColor);

      .heading-item {
        text-align: center;
        padding: 9px 0;
      }

      .item-border {
        border-left: 1px solid #FFFFFF;
        border-right: 1px solid #FFFFFF;
      }
    }

    .table-body {
      .table-body-item {
        text-align: center;
        padding: 7px 0 3px;
        border-bottom: 1px solid #F5F5F5;
      }

      .highlight {
        background-color: v-bind(currentProgressColor);
      }

      .item-text {
        height: 20px;
        line-height: 20px;
      }

      .item-border {
        border-left: 1px solid #F5F5F5;
        border-right: 1px solid #F5F5F5;
      }

      .item-icon {
        width: 20px;
        height: 20px;
      }
    }
  }

  .footer-box {
    position: fixed;
    bottom: 0;
    width: 100vw;
    color: #3D3D3D;
    padding: 9px 12px;
    box-sizing: border-box;
    background-color: #FFFFFF;

    .footer-top {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .top-left {
        display: flex;
        align-items: center;
        font-size: 13px;
      }

      .top-right {
        font-size: 10px;
      }
    }

    .progress-box {
      margin: 9px 0;
    }

    .footer-btn {
      color: v-bind(currentColor);
      font-size: 16px;
      text-align: center;
      height: 44px;
      font-weight: 600;
      line-height: 44px;
      margin-bottom: 15px;
      border-radius: 22px;
      background-color: v-bind(currentHeadColor);
    }
  }
}

.icon-img {
  width: 18px;
  height: 18px;
}
</style>