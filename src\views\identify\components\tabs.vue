<template>
  <div class="tabs-container" v-if="tabs?.length">
    <van-tabs class="tab-box"
              v-model:active="props.category"
              @click-tab="tabChange"
              ref="tabsRef"
              color="#478E87"
              line-width="28"
              line-height="2"
              title-active-color="#478E87"
              title-inactive-color="#3D3D3D">
      <van-tab :title="tab.name" :name="tab.id" v-for="(tab, index) of tabs" :key="index" />
    </van-tabs>
  </div>
</template>

<script setup>
import {ref, onMounted} from "vue";
import { getCache } from "@/utils/cache.js";
import {commonCategory} from "@/views/identify/lists.js";

const props = defineProps({
  category: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['change'])

const tabs = commonCategory.value



onMounted(() => {
  // let list = JSON.parse(getCache('CATEGORY_LIST'))
  // tabs.value = list
})

const tabChange = (item) => {
  emit('change', item)
}
</script>

<style scoped lang="scss">
.tabs-container {
  .van-tabs {
    --van-tabs-line-height: 28px;
  }
  .tab-box {
    border-bottom: 1px solid #F5F5F5;
  }
}
</style>