// import { createRouter, createWebHistory } from 'vue-router'
// import identify from "@/router/identify.js";
// import search from "@/router/search.js";
//
// const routes = [
//     {
//         path: '/',
//         redirect: '/identify/index'
//     },
//     {
//         path: '/identify/index',
//         name: 'identify',
//         component: import("./../views/identify/index.vue"),
//     },
//     ...identify,
//     ...search,
// ]
//
// const router = createRouter({
//     history: createWebHistory(import.meta.env.BASE_URL), // History 模式
//     routes
// });
//
// export default router

// export default router
import { createRouter, createWebHashHistory } from 'vue-router'
import identify from "@/router/identify.js";
import search from "@/router/search.js";

const routes = [
    {
        path: '/',
        redirect: '/identify/index'
    },
    {
        path: '/identify/index',
        name: 'identify',
        component: import("./../views/identify/index.vue"),
    },
    ...identify,
    ...search,
]

const router = createRouter({
    history: createWebHashHistory(), // 使用哈希模式
    routes
});

export default router


