<template>
  <div class="data-statistics-page">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      :style="{ height: `calc(100vh - 10px - 49px - ${titleHeight}px)`,paddingTop: `${titleHeight}px`}">
      <div class="statistics-container">
        <div class="all-container">
          <div class="all-count-box">
            <div class="all-count">{{ recentData?.allNum }}</div>
            <div>近期求鉴</div>
          </div>
          <div class="all-classify">
            <div class="all-jd-box">
              <div class="jd-icon">
                <img class="icon" src="https://www.badmintoncn.com/cbo_ea/img/pc/common/<EMAIL>"/>
              </div>
              <div class="jd-text-box">
                <div class="text-count">{{ recentData?.appraisalNum }}</div>
                <div>已被鉴定</div>
              </div>
            </div>
            <div class="vertical-line"></div>
            <div class="all-jd-box">
              <div class="jd-icon">
                <img class="icon" src="https://www.badmintoncn.com/cbo_ea/img/pc/common/<EMAIL>"/>
              </div>
              <div class="jd-text-box">
                <div class="text-count">{{ recentData?.recent?.real }}</div>
                <div>鉴定看正</div>
              </div>
            </div>
            <div class="vertical-line"></div>
            <div class="all-jd-box">
              <div class="jd-icon">
                <img class="icon" src="https://www.badmintoncn.com/cbo_ea/img/pc/common/<EMAIL>"/>
              </div>
              <div class="jd-text-box">
                <div class="text-count">{{ recentData?.recent?.fake }}</div>
                <div>鉴定看假</div>
              </div>
            </div>
          </div>
        </div>
        <div class="brand-container">
          <div class="brand-title">近期各品牌数据统计</div>
          <div style="height: 1px; background-color: #F5F5F5"></div>
        </div>
        <div class="brand-list-container">
          <div class="brand-list-box" v-for="item in brandDataList">
            <div class="brand-list-top">
              <div class="brand-left">
                <img class="brand-icon" :src="item.logo"/>
                <div class="brand-text">
                  <div class="brand-count">{{ `鉴定总数${item?.stats.all?.real + item?.stats.all?.fake}` }}</div>
                  <div>
                    {{ `看正${item?.stats.all?.real}｜看假${item?.stats.all?.fake}｜看假率${item?.stats.all?.rate}%` }}
                  </div>
                </div>
              </div>
              <div class="brand-right">
                收起
                <van-icon name="arrow-down" size="11"/>
              </div>
            </div>
            <div class="brand-circle-box">
              <VChart :options="chartOptions" autoresize />
            </div>
          </div>
        </div>
      </div>
    </van-pull-refresh>
    <TabBar :page="3"/>
  </div>
</template>
<script setup>
import {ref, onMounted} from "vue";
import {useAuthStore} from "@/stores/auth.js";
import commonApi from "@/services/common";
import TabBar from "@/components/tabbar.vue";

const authStore = useAuthStore();
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart, LineChart, BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';

// 2. 手动注册必需的 ECharts 组件
// 这是减小打包体积的关键步骤，必须确保你用到的组件都注册了
use([
  CanvasRenderer,
  PieChart,      // 我们要用饼图
  LineChart,     // 示例中也注册了折线图和柱状图，方便你以后测试
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);



const refreshing = ref(false)
const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
const recentData = ref({})
const brandDataList = ref([])
const brandList = ref([
  {
    id: 1,
    name: "尤尼克斯",
    ename: "yonex",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/yonex.png"
  },
  {
    id: 22,
    name: "李宁",
    ename: "lining",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/lining.png"
  },
  {
    id: 2,
    name: "威克多",
    ename: "victor",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/victor.png"
  },
  {
    id: 0,
    name: "其他",
    ename: "other",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/others.png"
  },
])
const brandConfig = [
  {name: '球拍', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymqp.png", dataKey: 'racket'},
  {name: '球鞋', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymqx.png", dataKey: 'sneakers'},
  {name: '羽毛球', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymq.png", dataKey: 'birdie'},
  {name: '球拍线', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/ymqpx.png", dataKey: 'line'},
  {name: '其他', logo: "https://www.badmintoncn.com/cbo_ea/img/pc/tabIcon/qt.png", dataKey: 'other'},
];


const chartOptions = ref({
  title: {
    text: '简单饼图示例',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    data: ['直接访问', '搜索引擎', '联盟广告']
  },
  series: [
    {
      name: '访问来源',
      type: 'pie', // 图表类型为饼图
      radius: '50%',
      data: [      // 静态数据
        { value: 335, name: '直接访问' },
        { value: 310, name: '搜索引擎' },
        { value: 234, name: '联盟广告' },
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
});


const onRefresh = async () => {
  // await getUserMes() 重新刷新当前页面数据
  refreshing.value = false
}

const getRecentData = async () => {
  let res = await commonApi.getStatistics({zone: 'recent'})
  if (res.code === 200) {
    recentData.value = res.data.list
    brandDataList.value = brandList.value.map(brand => {
      const stats = recentData.value[brand.ename] || {}; // 根据 ename 匹配，找不到用空对象
      return {
        ...brand,
        stats
      };
    });
  }
  console.log('brandDataList', brandDataList.value)
}

// const VChart = ref(null)


onMounted(async () => {
  await getRecentData()
  window.mag.setData({
    shareData: {
      title: `数据统计`,
      des: `近期数据统计`,
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: `${window.location.href}`,
    }
  });
})

if (authStore.phone) {
  window.mag.setTitle('近期数据统计');
  window.mag.setNavigationColor('#8629FF');
  window.mag.setNavigationBarStyle({
    navi_transparent: 1, // 透明导航栏
    navi_transparency: 1,// 透明度为 0
    navi_style: 1, // 白色导航栏文字
  });
} else {
  document.title = '近期数据统计'
}

window.mag.showNavigation()
</script>

<style lang="scss" scoped>
.data-statistics-page {
  height: 100%;
  width: 100vw;
  background-color: #FFFFFF;

  .content-table-list {
    overflow: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .statistics-container {
    .all-container {
      color: #FFFFFF;
      padding: 5px 16px 26px;
      height: 126px;
      background: linear-gradient(180deg, #8629FF 0%, #5D8DFF 100%);

      .all-count-box {
        display: flex;
        align-items: baseline;
        font-size: 11px;
        font-weight: 400;

        .all-count {
          font-size: 30px;
          font-weight: 700;
          margin-right: 6px;
        }
      }

      .all-classify {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 26px;

        .all-jd-box {
          display: flex;
          align-items: center;

          .jd-icon {
            position: relative;
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.9);

            .icon {
              width: 24px;
              height: 24px;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }

          .jd-text-box {
            font-size: 11px;
            font-weight: 400;
            margin-left: 5px;

            .text-count {
              font-size: 18px;
              font-weight: 500;
              margin-bottom: 4px;
            }
          }
        }
      }

      .vertical-line {
        height: 34px;
        width: 1px;
        background-color: #FFFFFF;
        opacity: 0.1;
      }
    }

    .brand-container {
      margin-top: -30px;
      border-radius: 15px 15px 0 0;
      background-color: #FFFFFF;

      .brand-title {
        color: #3D3D3D;
        font-size: 16px;
        font-weight: 600;
        padding: 15px 12px 12px;
      }
    }

    .brand-list-container {
      margin: 0 12px;

      .brand-list-box {
        margin: 20px 0;

        .brand-list-top {
          display: flex;
          justify-content: space-between;

          .brand-left {
            display: flex;
            align-items: center;

            .brand-icon {
              width: 72px;
              height: 40px;
              border-radius: 5px;
            }

            .brand-text {
              color: #666666;
              font-size: 12px;
              font-weight: 400;
              margin-left: 10px;

              .brand-count {
                color: #3D3D3D;
                font-size: 14px;
                font-weight: 400;
                margin-bottom: 6px;
              }
            }
          }

          .brand-right {
            color: #999999;
            font-size: 11px;
            font-weight: 400;
          }
        }
        .brand-circle-box {
          width: 100%;
          height: 400px; /* 关键！必须给一个高度 */
          border: 1px solid #eee; /* 加个边框让你能看清容器的范围 */
          margin-top: 20px;
        }

      }
    }

  }
}
</style>