<template>
  <van-popup
      v-model:show="delOverlay"
      round
      position="bottom"
  >
    <div class="btn-container">
      <div class="type-btn" @click="handleDel">删除</div>
      <div class="type-line" v-if="authStore.phone"/>
      <div class="type-btn" v-if="authStore.phone" @click="handleAppChange('camera')">拍摄更换</div>
      <div class="type-line" v-if="authStore.phone"/>
      <div class="type-btn" v-if="authStore.phone" @click="handleAppChange('album')">从相册更换</div>
      <div class="type-line" v-if="!authStore.phone"/>
      <div class="type-btn" v-if="!authStore.phone" @click="handleChange">更换图片</div>
      <div v-if="type !== '发布补图'" class="type-line"/>
      <div v-if="type !== '发布补图'" class="type-btn" @click="handleCover">
        {{ item.cover ? '取消当前封面' : '设置为封面' }}
      </div>
      <div class="division"/>
      <div class="type-btn" @click.stop="delOverlay = false">取消</div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref, computed} from 'vue'
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();

const delOverlay = ref(false)

const props = defineProps({
  item: {
    type: Object,
    default: () => {
    }
  },
  type: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['del', 'change', 'appChange', 'cover'])

const handleDel = () => {
  emit('del')
}

const handleChange = () => {
  emit('change')
}

const handleAppChange = (type) => {
  emit('appChange', type)
}

const handleCover = () => {
  emit('cover')
}

// 暴露方法
const open = () => delOverlay.value = true
const hide = () => delOverlay.value = false


defineExpose({open, hide})
</script>

<style scoped lang="scss">
.btn-container {
  .type-btn {
    color: #333333;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    padding: 20px 0;
  }

  .type-line {
    margin: 0 20px;
    height: 1px;
    background-color: #F5F5F5;
  }

  .division {
    height: 5px;
    background-color: #F5F5F5;
  }
}
</style>