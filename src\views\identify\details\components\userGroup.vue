<template>
  <div class="avatar-group-container">
    <!-- 头像组 -->
    <div class="avatar-stack">
      <div
          v-for="(item, index) in visibleAvatars"
          :key="item.id"
          class="avatar-item"
          :class="{'red-item': !trueShow}"
          :style="{
          zIndex: visibleAvatars.length - index,
          marginLeft: index > 0 ? '-12px' : 0
        }"
      >
        <img class="avatar-image" :src="item?.user?.avatar" @click="handleAvatarClick(item)">
      </div>

      <!-- 显示更多数量 -->
      <div
          v-if="showMoreCount"
          class="more-count"
          :style="{ marginLeft: '-12px' }"
      >
        <img class="avatar-image" :src="userMoreIcon"/>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue';
import userMoreIcon from "@/assets/images/details/userMore.png"

const props = defineProps({
  // 头像数据源
  avatars: {
    type: Array,
    default: () => []
  },
  // 最大显示数量
  maxVisible: {
    type: Number,
    default: 5
  },
  //看正还是看假
  trueShow: {
    type: Boolean,
    default: true
  }
});

// 计算显示的头像列表
const visibleAvatars = computed(() => {
      if (props.avatars) {
        return props.avatars.slice(0, props.maxVisible)
      }
    }
);

// 计算剩余数量
const moreCount = computed(() => {
      if (props.avatars) {
        return props.avatars.length - props.maxVisible
      }
    }
);

// 是否显示更多数量
const showMoreCount = computed(() =>
    moreCount.value > 0
);

// 点击头像回调
const handleAvatarClick = (avatar) => {
  console.log('点击头像:', avatar);
};
</script>

<style scoped>
.avatar-group-container {
  padding: 10px 12px 15px;
}

.avatar-stack {
  display: flex;
  align-items: center;
}

.avatar-item {
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
  cursor: pointer;
  border: 1px solid #B3E2DE;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.red-item {
  border: 1px solid #FFBEBE;
}

.avatar-image {
  width: 32px;
  height: 32px;
  border: none !important;
  border-radius: 50%;
}

/*.avatar-item:hover {*/
/*  transform: translateY(-2px);*/
/*}*/

.more-count {
  width: 32px;
  height: 32px;
  background: #f0f2f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  /*border: 2px solid #fff;*/
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>