<template>
  <van-popup
      v-model:show="showPopup"
      position="bottom"
      round
      :style="{ height: '80%' }"
  >
    <div class="mention-page">
      <div class="top-box">
        <div class="title-container">
          <div class="cancel" @click="hide">取消</div>
          <div class="title"> @用户</div>
          <div class="cancel" style="font-size: 14px">{{ `已选${activeUser.length}人` }}</div>
        </div>
        <div class="search-container">
          <van-search v-model="keyword" placeholder="请输入用户昵称" shape="round" @update:model-value="handleChange">
            <template #left-icon>
              <img class="search-icon" :src="searchIcon">
            </template>
          </van-search>
        </div>
      </div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list mb-80">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad"
            ref="listRef"
            :error.sync="errorStatus"
            :immediate-check="false"
        >
          <div v-if="oftenList.length && careFlag" class="user-container">
            <div class="user-title">最近常@</div>
            <div class="user-mes-box">
              <div v-for="(item, index) in oftenList" :key="index">
                <div class="flex-vc justify-between">
                  <div class="user-mes" @click="jumpUserPage(item)">
                    <img class="user-avatar" :src="item?.avatar || item?.user?.avatar"/>
                    <div class="user-desc">
                      <div class="name">{{ item?.name || item.user?.name }}</div>
                      <div class="count-box">
                        <GradeTag :group-id="Number(item?.user?.groupId) || Number(item.groupId)"
                                  :userLevel="item?.user?.userLevel || item?.userLevel"
                                  :uid="item?.user?.uid"/>
                        <div
                            v-if="![1, 2].includes(item.user?.groupId ?? item.groupId) && Number(item?.user?.uid ?? item.uid) !== 117229">
                          <div class="shield-box" v-if="Number(item.user?.countRecent ?? item.countRecent) !== 0"
                               :class="{'shield-box-active': (Number(item?.user?.diffRecent ?? item.diffRecent)) !== 0,}">
                            <img v-if="item.user?.countRecent !== 0" class="shield-icon"
                                 :src="Number(item.user?.diffRecent ?? item.diffRecent) === 0 ? greenShield : redShield"/>
                            <div
                                v-if="item.user?.countRecent !== 0 && Number(item?.user?.diffRecent ?? item.diffRecent) !== 0"
                                class="shield-text">{{ `${item?.user?.diffRecent ?? item.diffRecent}%` }}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="desc-box">
                        <div class="desc" v-if="item?.user?.brand">{{ `擅长：${item?.user?.brand}` }}</div>
                      </div>
                    </div>
                  </div>
                  <img
                      v-if="getItemStatus(item) === 'checked'"
                      class="check-img"
                      @click="handleUser(item)"
                      :src="checkedIcon"
                  />
                  <img
                      v-else-if="getItemStatus(item) === 'unchecked'"
                      class="check-img"
                      @click="handleUser(item)"
                      :src="unCheckIcon"
                  />
                </div>
                <van-divider class="line-divider" :style="{borderColor: 'f5f5f5'}"/>
              </div>
            </div>
          </div>
          <div v-if="oftenList.length && careFlag" class="line"/>
          <div class="user-container">
            <div v-if="careFlag" class="user-title mb-8 flex">
              <div :style="{color: careType === 1 ? '#3D3D3D' : '#999999'}" @click="handleCareType(1)">首鉴先锋</div>
              <div class="ml-20" :style="{color: careType === 2 ? '#3D3D3D' : '#999999'}" @click="handleCareType(2)">
                热心球友
              </div>
              <div class="ml-20" :style="{color: careType === 3 ? '#3D3D3D' : '#999999'}" @click="handleCareType(3)">
                全部已关注
              </div>
            </div>
            <div class="user-mes-box">
              <div v-for="(item, index) in careList" :key="index">
                <div class="flex-vc justify-between">
                  <div class="user-mes" @click="jumpUserPage(item)">
                    <img class="user-avatar" :src="item?.avatar || item.user?.avatar"/>
                    <div class="user-desc">
                      <div class="flex-vc">
                        <div class="name">{{ item?.name || item.user?.name }}</div>
                      </div>
                      <div class="count-box">
                        <GradeTag :groupId="Number(item?.user?.groupId) || Number(item.groupId)"
                                  :uid="item?.user?.uid"
                                  :userLevel="item?.user?.userLevel || item?.userLevel"/>
                        <div
                            v-if="![1, 2].includes(item.user?.groupId ?? item.groupId) && Number(item?.user?.uid ?? item.uid) !== 117229">
                          <div class="shield-box" v-if="Number(item.user?.countRecent ?? item.countRecent) !== 0"
                               :class="{'shield-box-active': (Number(item?.user?.diffRecent ?? item.diffRecent)) !== 0,}">
                            <img v-if="item.user?.countRecent !== 0" class="shield-icon"
                                 :src="Number(item.user?.diffRecent ?? item.diffRecent) === 0 ? greenShield : redShield"/>
                            <div
                                v-if="item.user?.countRecent !== 0 && Number(item?.user?.diffRecent ?? item.diffRecent) !== 0"
                                class="shield-text">{{ `${item?.user?.diffRecent ?? item.diffRecent}%` }}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="desc-box" v-if="item?.user?.brand">
                        <div class="desc">{{ `擅长：${item?.user?.brand}` }}</div>
                      </div>
                      <div class="desc-box">
                        <!--                      <div v-if="item?.groupId" class="tag mr-10"-->
                        <!--                           :class="[Number(item?.groupId) === 1 ? 'orange' :-->
                        <!--                Number(item?.groupId) === 2 ? 'orange' :-->
                        <!--                Number(item?.groupId) === 40 ? 'golden' :-->
                        <!--                Number(item?.groupId) === 3 ? 'green' :-->
                        <!--                Number(item?.gender) === 2? 'pink' : 'purple']">-->
                        <!--                        {{ userGroupList[item?.groupId].groupName }}-->
                        <!--                      </div>-->
                        <div class="desc">{{ item.sign }}</div>
                      </div>
                    </div>
                  </div>
                  <img
                      v-if="getItemStatus(item) === 'checked'"
                      class="check-img"
                      @click="handleUser(item)"
                      :src="checkedIcon"
                  />
                  <img
                      v-else-if="getItemStatus(item) === 'unchecked'"
                      class="check-img"
                      @click="handleUser(item)"
                      :src="unCheckIcon"
                  />
                </div>
                <van-divider class="line-divider" :style="{borderColor: 'f5f5f5'}"/>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
      <div class="footer-box" :class="{'ios-footer': IS_IOS}">
        <div class="footer-btn left-btn" @click="showPopup = false">取消</div>
        <div class="footer-btn right-btn" @click="handleConfirm">确定</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref, computed, nextTick} from "vue";
import {debounce} from "lodash";
import {getCache, setCache} from "@/utils/cache.js";
import searchIcon from "@/assets/images/identify/searchIcon.png"
import greenShield from "@/assets/images/common/greenShield.png"
import redShield from "@/assets/images/common/redShield.png"
import {useAuthStore} from "@/stores/auth.js";
import identifyApi from "@/services/identify.js";
import GradeTag from "@/views/identify/grade/components/gradeTag.vue";
import checkedIcon from '@/assets/images/common/checkedIcon.png'
import unCheckIcon from '@/assets/images/common/unCheckIcon.png'
import {isIOS, jumpUser} from "@/utils/common.js";


const IS_IOS = isIOS()

const authStore = useAuthStore();

const showPopup = ref(false);
const keyword = ref()
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const listRef = ref()
const finishedText = ref('没有更多了')
const errorStatus = ref(false)
const careFlag = ref(false)
const careType = ref(1)
const oftenList = ref([])
const atUser = ref([])
const activeUser = ref([])
const activeUserAll = ref([])
const careList = ref([])
const paramsSize = ref({
  page: 1,
  pageSize: 10,
})


const props = defineProps({
  brandId: {
    type: [String, Number],
    default: 0
  },
  cateId: {
    type: [String, Number],
    default: 0
  }
})


const emit = defineEmits(['addUser'])

const handleRatio = (item) => {
  if (Number(item?.user?.diffRecent) > 0) {
    showToast('表态少数方占比')
  } else {
    showToast('表态多数方占比')
  }
}

const getItemStatus = computed(() => {
  return (item) => {
    const uid = item?.uid || item?.user?.uid;
    if (!uid) return 'disabled';
    const isSelected = activeUser.value?.includes(uid);
    if (isSelected) {
      return 'checked';
    } else if (activeUser.value.length < 12) {
      return 'unchecked';
    }
  };
});

const userGroupList = computed(() => {
  return JSON.parse(getCache('USER_GROUP_LIST')).reduce((obj, item) => {
    obj[item.groupId] = item
    return obj
  }, {})
})

const userInfo = computed(() => {
  return authStore.userInfo
})


const onRefresh = () => {
  paramsSize.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    fetchFans();
  })
}

const onLoad = () => {
  if (careList.value) {
    paramsSize.value.page++
    nextTick(() => {
      fetchFans();
    })
  }
};

const jumpUserPage = (item) => {
  const uid = item?.uid || item?.user?.uid;
  jumpUser(uid)
}

const handleUser = (item) => {
  const uid = item?.uid || item?.user?.uid;
  const existingIndex = activeUser.value.indexOf(uid);

  if (existingIndex !== -1) {
    // 取消选中
    activeUser.value.splice(existingIndex, 1);
    const userInfoIndex = activeUserAll.value.findIndex(user => user.id === uid);
    if (userInfoIndex !== -1) {
      activeUserAll.value.splice(userInfoIndex, 1);
    }
  } else {
    activeUser.value.push(uid);
    let mentionUserMap = {};
    const cachedMap = getCache('MENTION_USER_MAP');
    if (cachedMap) {
      try {
        mentionUserMap = JSON.parse(cachedMap);
      } catch (e) {
        console.error("解析 MENTION_USER_MAP 缓存失败", e);
        mentionUserMap = {};
      }
    }
    mentionUserMap[uid] = {
      count: (mentionUserMap[uid]?.count || 0) + 1,
      lastTimestamp: Date.now() // 记录当前时间戳
    };
    setCache('MENTION_USER_MAP', JSON.stringify(mentionUserMap));
    const userInfo = {
      name: item.name || item.user.name,
      id: uid,
    };
    const existingInfoIndex = activeUserAll.value.findIndex(user => user.id === uid);
    if (existingInfoIndex == -1) {
      activeUserAll.value.push(userInfo);
    }
  }
};

const handleConfirm = () => {
  // if(activeUserAll.value) {
  //   activeUserAll.value.forEach((item) => {
  //     emit('addUser', item);
  //   })
  // }
  emit('addUser', activeUserAll.value);
  hide()
}

const handleCareType = (type) => {
  if (type === careType.value) return
  careType.value = type
  careList.value = []
  paramsSize.value.page = 1
  fetchFans()
}


const fetchFans = async () => {
  let paramsCopy;
  let apiMethod;
  if (careType.value === 3 && !keyword.value) {
    paramsCopy = {...paramsSize.value, uid: userInfo.value?.id};
    apiMethod = 'getUserList';
  } else {
    paramsCopy = keyword.value
        ? {...paramsSize.value, uid: userInfo.value?.id, name: keyword.value}
        : {...paramsSize.value, type: careType.value, brand: props.brandId, cate: props.cateId};
    apiMethod = keyword.value ? 'getUserList' : 'getRanking';
  }
  loading.value = true;
  try {
    // const res = await identifyApi[keyword.value ? 'getUserList' : 'getRanking'](paramsCopy);
    const res = await identifyApi[apiMethod](paramsCopy);
    if (res.code === 200) {
      if (refreshing.value) {
        careList.value = [];
        refreshing.value = false;
        finished.value = false;
      }

      const {list = [], total = 0} = res.data || {};
      if (list) {
        careList.value = careList.value.concat(list);
      } else {
        careList.value = []
      }
      finished.value = total <= careList.value.length;
      finishedText.value = careList.value.length === 0 ? "" : "没有更多了";
    } else {
      errorStatus.value = true;
    }
  } catch (error) {
    errorStatus.value = true;
  } finally {
    loading.value = false;
  }
};


// const fetchFans = async () => {
//   console.log('document.cookie----', document.cookie)
//   try {
//     loading.value = true
//     const cookie =  document.cookie
//     const url = `https://app.badmintoncn.com/mag/user/v1/UserNotify/MyFollowsList`
//     const params = {
//       p: paramsSize.value.page,
//       user_id: userInfo.value.id,
//       step: paramsSize.value.pageSize,
//       name: keyword.value
//     }
//     const response = await axios.get(url, {
//       params,
//       headers: {'Cookie': cookie},
//       withCredentials: true,
//       timeout: 10000
//     })
//     console.log('response', response)
//     // if (response.status !== 200) {
//     //   throw new Error(`HTTP error! status: ${response.status}`)
//     // }
//     const resData = response.data
//     if (resData.code !== 100) {
//       throw new Error(resData.message || 'API返回错误')
//     }
//     const data = resData.data || {}
//     const records = data.list || []
//     careList.value = refreshing.value ? records : [...careList.value, ...records]
//     finished.value = careList.value.length >= (data.total || 0)
//   } catch (error) {
//     console.error('请求失败:', error)
//     // 重要：标记已完成防止无限重试
//     finished.value = true
//   } finally {
//     loading.value = false
//     refreshing.value = false
//   }
// }


const handleChange = debounce(async () => {
  careList.value = []
  // oftenList.value = []
  paramsSize.value.page = 1
  paramsSize.value.pageSize = 10
  if (keyword.value) {
    careFlag.value = false
    await fetchFans()
    // careFlag.value = false
    // let res = await fetchFans(fetchFans)
    // const { code, data } = res
    // if(code === 200) {
    //   careList.value = data.list
    // }
  } else {
    careFlag.value = true
    careList.value = []
    await fetchFans()
  }

}, 400)


const queryUser = async (idList) => {
  let res = await identifyApi.queryUserList({
    ids: idList.join(',')
  })
  if (res.code === 200) {
    oftenList.value = res.data.list
  }
}

//从缓存中读取 MENTION_USER_MAP，按 count 排序后取前5个 ID，
const updateOftenListFromCache = async () => {
  const cachedMap = getCache('MENTION_USER_MAP');
  if (cachedMap) {
    try {
      const mentionUserMap = JSON.parse(cachedMap);
      // 将对象转换为 [id, { count, lastTimestamp }] 数组
      const userEntries = Object.entries(mentionUserMap);
      // 1. 按 count 降序排序  2. 如果 count 相同，则按 lastTimestamp 降序排序（时间戳大的，也就是最近点的，排在前面）
      const sortedEntries = userEntries.sort((a, b) => {
        // a[1] 和 b[1] 是 { count, lastTimestamp } 对象
        const countDiff = b[1].count - a[1].count;
        if (countDiff !== 0) {
          return countDiff;
        }
        return b[1].lastTimestamp - a[1].lastTimestamp;
      });
      const sortedIds = sortedEntries.slice(0, 5).map(item => item[0]);
      if (sortedIds.length > 0) {
        await queryUser(sortedIds);
      } else {
        oftenList.value = [];
      }
    } catch (e) {
      oftenList.value = [];
    }
  } else {
    oftenList.value = [];
  }
};

const open = async (userAt) => {
  careType.value = 1
  careList.value = []
  oftenList.value = []
  atUser.value = []
  activeUser.value = []
  activeUserAll.value = []
  paramsSize.value.page = 1
  await fetchFans()
  await updateOftenListFromCache()
  userAt?.forEach((user) => {
    activeUser.value.push(user.id)
  })
  careFlag.value = true
  showPopup.value = true;
};

const hide = () => {
  oftenList.value = []
  atUser.value = []
  careList.value = []
  keyword.value = ''
  paramsSize.value.page = 1
  showPopup.value = false;
};


defineExpose({open, hide});
</script>

<style scoped lang="scss">
.mention-page {
  .top-box {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: #FFFFFF;
  }

  .title-container {
    padding: 18px 12px 15px;
    font-size: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .cancel {
      //flex: 1;
      color: #666666;
      font-weight: 400;
    }

    .title {
      color: #3D3D3D;
      font-size: 17px;
    }
  }

  .search-container {
    padding: 0 12px 15px;

    .search-icon {
      height: 14px;
      width: 14px;
      margin-top: 6px;
    }

    .van-search {
      padding: 0;
    }
  }

  .user-container {
    margin-top: 5px;

    .user-title {
      color: #A8A8A8;
      font-size: 13px;
      font-weight: 400;
      padding: 0 12px;
    }

    .user-mes-box {
      .user-mes {
        display: flex;
        align-items: center;
        padding: 12px;

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 26px;
          margin-right: 8px;
        }

        .user-desc {
          .name {
            color: #3D3D3D;
            font-size: 14px;
          }

          .count-box {
            padding-top: 4px;
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 11px;
            color: #999999;
          }

          .desc-box {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 10px;
            margin-top: 3px;

            .tag {
              color: #FFFFFF;
              padding: 3px 3px;
              border-radius: 3px;
              background-color: #819FFF;
              margin-right: 5px;
            }

            .pink {
              background-color: #FF7CAA;
            }

            .purple {
              background-color: #819FFF;
            }

            .orange {
              background-color: #FF8F1F;
            }

            .golden {
              color: #3D3D3D;
              font-weight: 500;
              border: 1px solid #FFE3B9;
              background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
            }

            .green {
              background-color: #00AC72;
            }

            .desc {
              color: #999999;
            }
          }
        }
      }

      .check-img {
        width: 18px;
        height: 18px;
        margin-right: 16px;
      }
    }
  }

  .line {
    height: 5px;
    margin-bottom: 12px;
    background-color: #F5F5F5;
  }

  //.line-divider {
  //  padding-left: 120rpx;
  //}
  .van-divider {
    margin: 2px 0 0 60px;
  }

  .footer-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #FFFFFF;

    .footer-btn {
      text-align: center;
      width: 168px;
      height: 44px;
      line-height: 44px;
      font-size: 16px;
      border-radius: 22px;
      color: #478E87;
      background-color: #DFF7F6;
    }

    .right-btn {
      color: #FFFFFF;
      background-color: #478E87;
    }
  }

  .ios-footer {
    padding: 12px 12px 26px;
  }
}

::v-deep.van-popup {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  /* 隐藏滚动条 - Webkit浏览器 (Chrome, Safari, Edge等) */
  &::-webkit-scrollbar {
    display: none !important;
    width: 0;
    height: 0;
  }
}
</style>