<template>
  <Layout class="improve-page" :style="{ paddingTop: `${titleHeight}px`}">
    <div class="improve-container">
      <div class="title-name">{{ title }}</div>
      <div v-for="(item, index) in filteredList" :key="index">
        <div class="improve-list" :class="{'grey-bg': item.type === 2}">
          <div class="list-item">
            <div class="item-left flex-vhc">
              <img class="left-img" :src="item.icon"/>
              <span :class="{'yellow-text': item.type === 2}">{{ item.name }}</span>
            </div>
            <div class="item-right flex-vhc">
              <div>{{ item.desc }}</div>
              <div class="right-btn" @click="handleUp(item)">
                <span>推广</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="balance-box">
        <div>{{ `金币余额：${goldCount}` }}</div>
        <div class="balance-btn" @click="handleRecharge">
          <span>充值金币</span>
        </div>
      </div>
      <div class="line"></div>
      <div class="prompt-box">
        <div>温馨提示：</div>
        <div>1.可根据您的需要通过支付金币来自助推广求鉴；</div>
        <div>2.对于已推广的求鉴再次推广则继续累加推广时间；</div>
        <div>3.请确认推广的内容不涉及政治敏感、黄赌毒、引战等社区禁止话题，违反将被直接屏蔽，金币不退还；</div>
        <div>4.推广后的帖子不可取消推广，推广期内，删除原帖，推广金币不退回，有疑问请联系官方；</div>
      </div>
    </div>
    <template #footer>
      <div class="release-footer flex-col-hc" @click="handleMyPage">
        <div class="btn-text">我的求鉴</div>
      </div>
    </template>
    <CDialog
        ref="improveDialogRef"
        title="温馨提示"
        btnRightTxt="确定"
        :content="improveContent"
        @rightClick="improveUp"
    />
    <CDialog
        ref="commonDialogRef"
        title="温馨提示"
        btnRightTxt="去充值"
        :content="['当前金币余额不足','充值后方可继续推广']"
        @rightClick="handleRecharge"
    />
  </Layout>
</template>

<script setup>
import {ref, onMounted, computed} from "vue";
import {getQueryParam, jumpUser} from "@/utils/common.js";
import identifyApi from "@/services/identify.js";
import CDialog from "@/components/c-dialog.vue";
import Layout from "@/components/Layout.vue";
import moneyIcon from "@/assets/images/identify/money.png"
import goldIcon from "@/assets/images/identify/goldIcon.png"
import {useRoute} from "vue-router";
import {useRouter} from "vue-router";
import {jumpLogin , isMagApp} from "@/utils/common.js";

const route = useRoute()
const router = useRouter()
import {useAuthStore} from "@/stores/auth.js";
import axios from "axios";
import commonApi from "@/services/common.js";
import shareIcon from "@/assets/images/common/shareIcon.png";

const authStore = useAuthStore();
const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;

const userInfo = computed(() => {
  return authStore.userInfo
})


const freetype = route.query.freetype || getQueryParam('freetype')
const tid = route.query.id || getQueryParam('id')


const improveDialogRef = ref()
const improveContent = ref()
const commonDialogRef = ref()
const moneyNumber = ref()
const freNumber = ref()
const moneyTime = ref()
const goldCount = ref(0)
const title = ref('')
const detailInfo = ref()
const list = ref([
  {name: '免费', money: 0, fre: 0, order: 0, icon: moneyIcon, desc: '今日剩余10次', time: 0, type: 1},
  {name: '500金币', money: 500, fre: 100, order: 500, icon: goldIcon, desc: '推广1小时', time: 1, type: 2},
  {name: '2500金币', money: 2500, fre: 500, order: 2500, icon: goldIcon, desc: '推广5小时', time: 5, type: 2},
])


const filteredList = computed(() => {
  if (freetype === 'true') {
    return list.value.slice(1);
  } else {
    return list.value;
  }
})




const improveUp = async (money) => {
  let res = await identifyApi.upThread({
    uid: userInfo.value.id,
    tid: tid,
    spend: moneyNumber.value
  })
  if (res.code === 200) {
    await getListCount()
    if (freNumber.value === 0) {
      showToast('免费推广成功')
    } else {
      showToast(`已推广${freNumber.value}次曝光`)
    }
    setTimeout(() => {
      if (authStore.phone) {
        window.mag.closeWin();
      } else {
        router.back()
      }
    }, 1000)
  } else if (res.code === 4000) {
    commonDialogRef.value.show()
  } else if(res.code === 4001) {
    showToast(res.message)
  }
}


const handleUp = async (item) => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  moneyNumber.value = item.money
  freNumber.value = item.fre
  moneyTime.value = item.time
  if (!item.order) return showToast('今日剩余次数已用完')
  if (item.money === 0) {
    await improveUp(moneyNumber.value)
  } else {
    if (goldCount.value < item.money) {
      commonDialogRef.value.show()
    } else {
      improveContent.value = [
        `需扣除${moneyNumber.value}金币，预计超${freNumber.value}次曝光`,
        `持续${moneyTime.value}小时，确定推广？`
      ]
      improveDialogRef.value.show()
    }
  }
}


//获取提升次数
const getListCount = async () => {
  let res = await identifyApi.getEaCount({
    uid: userInfo.value.id,
    type: 2
  })
  if (res.code === 200) {
    let count = 10 - res.data.list
    if(list.value[0].name === '免费') {
      list.value[0].desc = `今日剩余${count}次`
      list.value[0].order = count
    }
  }
}

const getDetails = async () => {
  let res = await identifyApi.getEaDetails({id: (route.query.id || getQueryParam('id')), uid: userInfo.value.id})
  const {code, data} = res
  if (code === 200) {
    detailInfo.value = data.list
  }
}

const getGoldCount = async () => {
  // try {
  //   let url = `https://app.badmintoncn.com/mag/user/v1/GradeScore/userGoldGet?user_id=${userInfo.value.id}&secret=drXPXWmETaDxxsAA4SXjaQeZnbHKAkW7`
  //   const res = await axios.get(url)
  //   if (res.status === 200) {
  //     goldCount.value = res.data.data
  //   }
  // } catch (error) {
  //   console.error('失败:', error);
  // }
  let res = await commonApi.getGold({id: authStore.userInfo.id})
  if(res.code === 200) {
    goldCount.value = res.data.list.gold
  }
}


// const handlePay = () => {
//   let config = {
//     money: 1,
//     title: '测试支付',
//     desc: '这个用来测试支付',
//     payWay: {
//       wallet: 1,
//       weixin: 1,
//       alipay: 1,
//     },
//     orderNum:'123456',
//     unionOrderNum:'123456',
//     type:'打赏',
//   }
//   window.mag.pay(config, function(){
//     // 支付成功回调
//   }, function(){
//     // 支付失败回调
//   });
// }

const handleRecharge = () => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  if(isMagApp()) {
    window.mag.newWin(`https://www.badmintoncn.com/jq.php?mag_hide_progress=1`);
  } else {
    window.location.href = 'https://www.badmintoncn.com/jq.php?mag_hide_progress=1'
  }
}

const handleMyPage = () => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/myAppraisal/index?mag_hide_progress=1`);
  } else {
    router.push({
      name: 'myAppraisal'
    })
  }
}
window.mag.setData({
  shareData: {
    title: '求鉴推广',
    des: '',
    picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
    linkurl: window.location.href,
  }
});
if(authStore.phone) {
  window.mag.setTitle('求鉴推广');
} else {
  document.title = '求鉴推广'
}
window.mag.showNavigation()
window.mag.addRefreshComponent()

onMounted(() => {
  if(route.query.name || getQueryParam('name')) {
    let name = route.query.name || getQueryParam('name')
    title.value = decodeURIComponent(name)
  }
  decodeURIComponent()
  getListCount()
  getDetails()
  getGoldCount()
})
</script>

<style scoped lang="scss">
.improve-page {
  width: 100vw;
  height: 100vh;
  background-color: #FFFFFF;

  .improve-container {
    margin: 15px 12px;

    .title-name {
      color: #3D3D3D;
      font-size: 15px;
      line-height: 1.2;
      font-weight: 600;
    }

    .improve-list {
      margin-top: 15px;
      background-color: #D2EEE5;
      border-radius: 10px;

      .list-item {
        padding: 14px 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .item-left {
          color: #478E87;
          font-size: 17px;
          //font-weight: 5;
          .left-img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }
        }

        .item-right {
          flex-shrink: 0;
          color: #666666;
          font-size: 10px;
          font-weight: 400;

          .right-btn {
            margin-left: 8px;
            height: 28px;
            line-height: 28px;
            text-align: center;
            width: 59px;
            font-size: 13px;
            font-weight: 500;
            color: #FFFFFF;
            border-radius: 20px;
            background-color: #478E87;
          }
        }
      }
    }

    .grey-bg {
      background-color: #F5F5F5;
    }

    .yellow-text {
      color: #FF8F1F;
    }

    .balance-box {
      margin-top: 22px;
      color: #3D3D3D;
      font-size: 17px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .balance-btn {
        padding: 8px 15px;
        font-size: 13px;
        font-weight: 500;
        color: #478E87;
        border-radius: 22px;
        background-color: #DFF7F6;
      }
    }

    .line {
      margin: 22px 0 20px;
      height: 1px;
      background-color: #F5F5F5;
    }

    .prompt-box {
      color: #999999;
      font-size: 11px;
      line-height: 1.5;
      font-weight: 400;
    }
  }

  .release-footer {
    margin: 5px 12px 30px;
    color: #478E87;
    background-color: #DFF7F6;
    border-radius: 22px;

    .btn-text {
      padding: 11px 0;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>