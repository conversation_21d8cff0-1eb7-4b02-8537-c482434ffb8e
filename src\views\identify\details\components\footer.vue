<template>
  <div class="footer-page">
    <div class="btn-box" v-if="btnShow && (userInfo.groupId >= 12 || userInfo.groupId === 1 || userInfo.groupId === 2 || userInfo.groupId === 3)">
      <div class="btn" @click="handleOpinion(1)"><img class="btn left-btn" :src="greenBtn" /> <div class="btn-text">看正</div></div>
      <div class="btn" @click="handleOpinion(2)"><img class="btn right-btn" :src="redBtn" /> <div class="btn-text" >看假</div></div>
    </div>
     <div class="input-container-box":style="{'z-index': showPopup ? '10000' : ''}">
       <div class="input-container">
         <div class="input-box" @click="handleFocus">
           <div class="input-desc">
             <div class="flex-vhc">
               <img class="image-input-edit" :src="editIcon" />
               <div>说说自己的看法...</div>
             </div>
             <div class="paging-box" @click.stop="open">
               <div class="cur-page">{{ Math.ceil(total / 10) === 0 ? 0 : `${pageIndex}`}}</div>
               <div class="cur-line"></div>
               <div>{{`${Math.ceil(total / 10)}`}}</div>
             </div>
           </div>
         </div>
       </div>
       <div class="btn-container">
         <div class="icon-box mr-30" @click="handleComment(item)">
           <img class="image-icon" :src="plIcon"/>
           <div class="icon-text">{{item.posts || 0}}</div>
         </div>
         <div class="icon-box" @click="handleLike(item)">
           <img class="image-icon" :src="item.like ? ydzIcon : dzIcon"/>
           <div class="icon-text">{{item.likes || 0}}</div>
         </div>
       </div>
     </div>
    <div style="z-index: 9999;position: absolute; top: 0">
      <van-popup :show="showPopup" position="bottom" round @close="hide">
        <div class="pagination-container">
          <div class="title">
            <div @click="handleFirst">首页</div>
            <div style="font-weight: 600">翻页</div>
            <div @click="handleLast">末页</div>
          </div>
          <div class="container">
            <div class="center-box">
              <div v-for="item in Math.ceil(total / 10)" :key="item" @click="handleChange(item)">
                <div class="btn" :class="{'active-btn' : item === pageIndex}">{{ item }}</div>
              </div>
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<script setup>
import { ref, computed , watch} from "vue";
import editIcon from "@/assets/images/details/edit.png"
import plIcon from "@/assets/images/details/pl.png"
import dzIcon from "@/assets/images/details/dz.png"
import ydzIcon from "@/assets/images/details/ydz.png"
import greenBtn from "@/assets/images/details/botGreenBtn.png"
import redBtn from "@/assets/images/details/botRedBtn.png"
import identifyApi from "@/services/identify.js";
import {setCache} from "@/utils/cache";
import { useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();
import { useRouter } from "vue-router";
const router = useRouter()
import {jumpLogin, jumpUser} from "@/utils/common.js";


const message = ref()
const showPopup = ref(false)
const activeBtn = ref(1)

// 用户信息
const userInfo = computed(() => {
  return authStore.userInfo
})


const emit = defineEmits(['pageChange', 'focus', 'comment', 'opinion'])

const props = defineProps({
  item: {
    type: Object,
    default: {}
  },
  total: {
    type: Number,
    default: 0
  },
  pageIndex: {
    type: Number,
    default: 0
  },
  btnShow: {
    type: Boolean,
    default: false
  }
})

const handleFocus = () => {
  emit('focus')
}

const handleLike = async (item) => {
  if(!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  if (item.user?.uid === userInfo.value.id){
    showToast('抱歉，您不能为自己点赞')
    return
  }
  let res = await identifyApi.likeThread({
    uid: userInfo.value.id,
    tid: item.id
  })
  if(res.code === 200) {
    item.like = !item.like
    item.like ? item.likes++ : item.likes--
    item.like ? showToast('您已点赞') : showToast('您已取消点赞')
  }
}

// const handleCollect = (item) => {
//   if(!userInfo.value.id) {
//     jumpLogin()
//     return
//   }
// }

const handleComment = (item) => {
  if(!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  emit('comment')
}

// const handleShare = (item) => {
//   if(!userInfo.value.id) {
//     jumpLogin()
//     return
//   }
// }


const open = () => {
  showPopup.value = true
}

const hide = () => {
  showPopup.value = false
}

const handleOpinion = (type) => {
 emit('opinion', type)
}

const handleChange = (item) => {
  emit('pageChange', item)
  activeBtn.value = item
  hide()
}

const handleFirst = () => {
  activeBtn.value = 1
  emit('pageChange', 1)
  hide()
}

const handleLast = () => {
  activeBtn.value = Math.ceil(props.total / 10)
  emit('pageChange', activeBtn.value)
  hide()
}

// watch(() => props.pageIndex,
//     (val) => {
//       // activeBtn.value = val
//     })

</script>

<style lang="scss" scoped>
 .footer-page {
   padding-bottom: 10px;
   background-color: #FFFFFF;
   .btn-box {
     padding: 2px 24px 5px;
     display: flex;
     .btn {
       width: 163px;
       height: 28px;
       position: relative;
     }
     .btn-text {
       position: absolute;
       top: 50%;
       left: 50%;
       transform: translate(-50%, -30%);
       color: #FFFFFF;
       font-size: 16px;
       font-weight: 600;
     }
   }
   .input-container-box {
     padding: 7px 12px 17px 12px;
     position: relative;
     display: flex;
     align-items: flex-end;
     background-color: #FFFFFF
   }
   .input-container {
     width: 100%;
     display: flex;
     align-items: center;
     .input-box {
       color: #A8A8A8;
       font-size: 13px;
       font-weight: 400;
       width: 100%;
       border-radius: 5px;
       background-color: #F5F5F5;
       .input-desc {
         display: flex;
         align-items: center;
         justify-content: space-between;
         .image-input-edit {
           width: 16px;
           height: 16px;
           margin: 0 9px 0 11px;
         }
       }
     }
     .paging-box {
       padding: 4px 9px;
       border-radius: 10px;
       color: #999999;
       font-size: 10px;
       font-weight: 400;
       background-color: #F5F5F5;
       display: flex;
       flex-direction: column;
       align-items: center;
       .cur-page {
         color: #3D3D3D;
       }
       .cur-line {
         width: 15px;
         height: 1px;
         border-bottom: 1px solid #D1D1D1;
         transform: rotate(-10deg);
         margin: 2px 0;
       }
     }
   }
   .btn-container {
     display: flex;
     justify-content: space-between;
     align-content: center;
     align-items: center;
     padding-left: 20px;
     color: #3D3D3D;
     font-size: 13px;
     .icon-box {
       display: flex;
       align-items: center;
       flex-direction: column;
       .image-icon {
         width: 18px;
         height: 18px;
         margin-bottom: 2px;
       }
     }
   }
 }
 ::v-deep .u-input {
   padding: 20px 20px !important;
   background-color: #F5F5F5;
 }
 .pagination-container {
   padding: 20px 12px;
   .title {
     display: flex;
     justify-content: space-between;
     color: #333333;
     font-weight: 400;
     font-size: 16px;
     margin-bottom: 13px;
   }
   .container {
     height: 250px;
     overflow: auto;
     margin-bottom: 85px;
   }
   .center-box {
     overflow: hidden;
     display: flex;
     flex-wrap: wrap;
     .btn {
       color: #3D3D3D;
       font-weight: 400;
       font-size: 14px;
       margin: 10px 10px 0 0;
       width: 60px;
       height: 40px;
       line-height: 40px;
       text-align: center;
       border-radius: 5px;
       background-color: #F5F5F5;
       &:nth-child(4n) {
         margin-right: 0;
       }
     }
     .active-btn {
       color: #478E87;
       background-color: #DFF7F6;
     }
   }
 }
</style>