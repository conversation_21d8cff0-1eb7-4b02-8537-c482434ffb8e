<template>
  <div class="tags-page">
    <div
        v-for="(item, index) in brandList"
        :key="index"
        class="tag-item"
        :class="{'active-item': props.brand === item.id}"
        @click="toggleSelection(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted} from "vue";
import {getCache} from "@/utils/cache.js";

const props = defineProps({
  brand: {
    type: String,
    default: '全部'
  }
})
const emit = defineEmits(['change'])

const brandList = ref([])


const toggleSelection = (item) => {
  emit('change', item)
}


onMounted(() => {
  let list = JSON.parse(getCache('BRAND_LIST'))
  console.log('list', list)
  brandList.value = list
})

</script>

<style scoped lang="scss">
.tags-page {
  margin: 10px 12px;
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  column-gap: 9px;

  .tag-item {
    width: calc((100% - 45px) / 5);
    color: #3D3D3D;
    font-size: 10px;
    font-weight: 400;
    text-align: center;
    padding: 7px 0;
    border-radius: 5px;
    background-color: #F5F5F5;
  }

  .active-item {
    color: #478E87;
    font-weight: 500;
    background-color: #DFF7F6;
  }
}
</style>