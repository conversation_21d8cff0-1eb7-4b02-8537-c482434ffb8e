<template>
  <van-popup
      v-model:show="rewardShow"
      round
      position="bottom"
  >
    <div class="list-popup">
      <div class="title-box">
        <div></div>
        <div>奖励规则</div>
        <div></div>
      </div>
      <div class="content-box pt-20 pb-5">① 近期首鉴次数≥14次；</div>
      <div class="content-box pb-5">② 且鉴定量≥70次内含首鉴；</div>
      <div class="content-box pb-5">③ 鉴定等级需≥热心四级；</div>
      <div class="content-box">④ 近期异鉴率≤0.5%；</div>
      <div class="content-box pt-20 content-text">红包来源求鉴者的奖励，需同时满足上方所有条件并在规定时间内表态则有资格参与抽奖。</div>
      <div class="content-box pt-20 pb-5">时间说明： </div>
      <div class="content-box content-text">① 倒计时结束前参与鉴定可获抽取随机红包资格；若倒计时结束后参与鉴定的则无抽奖资格；</div>
      <div class="content-box content-text">② 根据求鉴者发布设置的红包个数，对应有不同的倒计时周期，倒计时结束后才可参与抽奖；</div>
      <div class="content-box content-text">③ 获得抽奖资格的鉴定者可在倒计时结束后6小时内主动抽取随机红包奖励，若抽奖时间结束还有剩余红包则系统将自动随机分配；</div>
      <div class="content-box content-text pt-20">鉴定结果：</div>
      <div class="content-box content-text">倒计时结束前表态后可见，倒计时结束后公开可见。</div>
      <div class="content-box content-text pt-20">其他说明：</div>
      <div class="content-box content-text pb-25">红包抽取后将转入中羽在线APP个人钱包里，满足一定额度可提现（需从APP操作）。</div>
      <div class="footer-btn" @click="changeKnow">
        我知道了
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref} from "vue";


const rewardShow = ref(false)
const emit = defineEmits(['close'])


const changeKnow = () => {
  hide()
  emit('close')
}


const show = () => {
  rewardShow.value = true
}
const hide = () => {
  rewardShow.value = false
}


defineExpose({show, hide})

</script>

<style lang="scss" scoped>
.list-popup {
  margin: 15px 12px 20px;
  box-sizing: border-box;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .title-tips {
    margin-top: 20px;
    color: #3D3D3D;
    line-height: 1.4;
    font-size: 15px;
  }

  .content-box {
    font-size: 14px;
    color: #3D3D3D;
    font-weight: 400;
  }

  .content-text {
    line-height: 1.4;
  }

  .footer-btn {
    color: #FFFFFF;
    font-size: 16px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #478E87;
  }
}
</style>