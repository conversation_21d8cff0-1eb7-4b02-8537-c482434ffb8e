<template>
  <div class="plate-container">
    <div class="swiper-box" @click="handleMore">
      <div class="title-box">
        <img class="left-icon" :src="homePlateTitle"/>
        <div class="right-box">
          <span>更多</span>
          <img class="right-icon" :src="moreRight"/>
        </div>
      </div>
      <div class="swiper-content">
        <van-swipe lazy-render :autoplay="3000" :show-indicators="false" @change="handleSwitch">
          <van-swipe-item class="list-content" v-for="(arr, index) in dataList" :key="index">
            <div class="list-box" v-for="val in arr">
              <div class="image-bg">
                <van-image v-if="val.picture" class="image-box" fit="cover"
                           :src="`${val.picture[0]}?imageView2/1/w/200/h/200/q/75/format/webp`">
                  <!--                <van-image v-if="val.picture" class="image-box" fit="cover"-->
                  <!--                           :src="`${picImage}?imageView2/1/w/200/h/200/q/75/format/webp`">-->
                  <template v-slot:loading>
                    <van-loading type="spinner" size="20"/>
                  </template>
                </van-image>
                <img v-if="!val.picture.length" class="image-box" :src="hollowIcon"/>
                <div class="image-top" v-if="val.real + val.fake !== 0 && !userMes?.train">
                  <div class="top-people" :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                    <img class="jd-icon" :src="shieldIcon"/>
                    <div class="pr-4">{{ val.real + val.fake }}</div>
                  </div>
                  <div class="top-ratio" v-if="val.real + val.fake >= 2"
                       :style="{ 'background-color': getBackgroundColor(val.real, val.fake) }">
                    {{ getDominantPercentage(val.real, val.fake) }}
                    <span class="per-text">%</span>
                  </div>
                </div>
                <div v-if="val?.train" class="image-top-right">训</div>
                <div class="background-layer"></div>
                <img v-if="val.appraisal !== null" class="verified-icon" :src="verifiedIcon"/>
                <div class="image-bot">
                  <div class="flex-vc">
                    <img v-if="val.brand === '其他'" class="bot-brand" :src="brandOther">
                    <img v-else class="bot-brand" :src="val.brandLogo"/>
                    <div class="bot-text">{{ `${val.cate}` }}</div>
                  </div>
                  <div class="bot-time">{{ val?.createTime }}</div>
                </div>
              </div>
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="indicators-box">
        <div v-for="(item, index) in dataList" :key="index">
          <div class="dot" :class="{'active-dot' : activeTab === index}"></div>
        </div>
      </div>
    </div>
    <div class="good-book" @click="handleJump">
      <div class="good-left">
        <img class="book-icon" :src="bookIcon"/>
        <div class="left-title">
          <div>鉴定好文</div>
          <div class="left-tips">了解更多鉴定知识</div>
        </div>
      </div>
      <div class="good-right">
        <div class="pr-2">去看看</div>
        <van-icon name="arrow" size="12"/>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, computed} from "vue";

import homePlateTitle from "@/assets/images/plate/homePlateTitle.png"
import moreRight from "@/assets/images/plate/moreRight.png"
import brandOther from "@/assets/images/details/brandOther.png"
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import verifiedIcon from "@/assets/images/identify/verifiedIcon.png"
import hollowIcon from "@/assets/images/common/hollowIcon.png"
import bookIcon from '@/assets/images/identify/bookIcon.png'
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
import {useRouter} from "vue-router";

const authStore = useAuthStore();
const router = useRouter()
const userInfo = computed(() => {
  return authStore.userInfo
})


const picImage = ref('https://q3.itc.cn/q_70/images03/20250507/3c137955e1524078bfa78ca998825567.jpeg',)

const params = ref({
  uid: userInfo?.value && userInfo.value.id ? userInfo.value.id : null,
  page: 1,
  pageSize: 6,
})

const userMes = ref({})
const dataList = ref([])
const activeTab = ref(0)

const handleSwitch = (index) => {
  console.log('index', index)
  activeTab.value = index
}

const handleMore = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/identify/index?mag_hide_progress=1`);
  } else {
    router.push({
      name: 'identify',
    })
  }
}

// 获取背景颜色
const getBackgroundColor = (real, fake) => {
  if (real + fake === 0) return '';
  if (real > fake) {
    return '#00B578';
  } else if (fake > real) {
    return '#FA5151';
  } else {
    return '#676f6e';
  }
};

// 获取主导方的百分比
const getDominantPercentage = (real, fake) => {
  const total = real + fake;
  if (total === 0) return ''; // 如果总和为 0，返回空字符串
  if (real > fake) {
    return `${Math.round((real / total) * 100)}`; // 看正的比率
  } else if (fake > real) {
    return `${Math.round((fake / total) * 100)}`; // 看假的比率
  } else {
    return '50'; // 如果相等，返回空字符串
  }
};

const arrayTo2D = (arr, columns) => {
  let result = [];
  for (let i = 0; i < arr.length; i += columns) {
    result.push(arr.slice(i, i + columns));
  }
  return result;
}


const getSearchList = () => {
  let paramsCopy = {
    ...params.value,
    order: 1,
  };
  // return;
  identifyApi.getSearch(paramsCopy).then((res) => {
    console.log('res', res)
    if (res.code === 200) {
      let list = res.data.list
      dataList.value = arrayTo2D(list, 2);
    }
    console.log('dataList.value', dataList.value)
  })
}


const handleJump = () => {
  let path = 'https://www.badmintoncn.com/thread.php?a=list&classid=35&mag_hide_progress=1'
  if (!authStore.phone) {
    window.open(path, '_blank')
  } else {
    alert('请下载中羽在线APP使用')
  }
}

onMounted(() => {
  getSearchList()
  if(document.getElementById('myTitle')) {
    document.getElementById('myTitle').style.display = "none";
  }
})
</script>

<style lang="scss" scoped>
.plate-container {
  width: 100%;
  height: 100%;
  background-color: #F8F8F8;
  padding-bottom: 11px;

  .swiper-box {
    margin: 20px 12px;
    background: linear-gradient(180deg, #A159FF 0%, #E5C8FF 100%);
    border-radius: 10px;

    .title-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;

      .left-icon {
        width: 138px;
        height: 18px;
      }

      .right-box {
        display: flex;
        align-items: center;
        color: #FFFFFF;
        font-size: 13px;
        font-weight: 400;

        .right-icon {
          width: 10px;
          height: 10px;
          margin-left: 1px;
        }
      }
    }

    .swiper-content {
      height: 161px;
      margin: 0 10px;

      .list-content {
        display: flex;
        justify-content: flex-start;
        flex-wrap: nowrap;
        column-gap: 9px;
        row-gap: 12px;

        .list-box {
          width: calc((100% - 9px) / 2);
          padding-left: 1px;

          .image-bg {
            position: relative;
            width: 100%;
            height: 161px;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 8px;

            .image-box {
              width: 100%;
              height: 161px;
              object-fit: cover;
              border-radius: 5px;
            }

            .image-top-right {
              position: absolute;
              top: 0;
              right: 0;
              color: #FFFFFF;
              font-size: 13px;
              padding: 3px;
              background-color: #9673FF;
              border-radius: 0 5px 0 5px;
            }

            .background-layer {
              position: absolute;
              display: flex;
              align-items: center;
              bottom: 0;
              height: 28px;
              width: 100%;
              border-radius: 0 0 5px 5px;
              background: rgba(0, 0, 0, 0.1);
              /* 关键模糊代码 */
              filter: blur(20px);
              /* 提升模糊效果 */
              transform: translateZ(0); /* 启用GPU加速 */
              -webkit-backface-visibility: hidden; /* 修复模糊边缘问题 */
            }

            .verified-icon {
              position: absolute;
              top: 46%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 110px;
              height: 110px;
              z-index: 1;
              opacity: 0.8;
              border-radius: 50%;
            }

            .image-bot {
              position: absolute;
              display: flex;
              align-items: center;
              justify-content: space-between;
              bottom: 0;
              width: 100%;
              border-radius: 0 0 5px 5px;
              backdrop-filter: blur(5px);
              -webkit-backdrop-filter: blur(5px);
              background: rgba(88, 88, 88, 0.3);

              .bot-brand {
                width: 40px;
                height: 18px;
                padding: 5px;
                border-radius: 8px;
              }

              .bot-text {
                color: #FFFFFF;
                font-weight: 600;
                font-size: 10px;
              }

              .bot-time {
                color: #FFFFFF;
                font-size: 10px;
                font-weight: 600;
                padding-right: 8px;
              }
            }
          }
        }
      }
    }

    .indicators-box {
      display: flex;
      justify-content: center;
      padding: 8px 0;

      .dot {
        width: 5px;
        height: 2px;
        margin-right: 3px;
        background-color: #FFFFFF;
        opacity: 0.5;
      }

      .active-dot {
        background-color: #FFFFFF;
        opacity: 1;
      }
    }
  }

  .good-book {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 12px 0;
    padding: 0 10px;
    background-color: #FFFFFF;
    border-radius: 10px 10px 10px 10px;

    .good-left {
      padding: 9px 0;
      display: flex;
      align-items: center;

      .book-icon {
        width: 36px;
        height: 36px;
        margin-right: 6px;
      }

      .left-title {
        color: #3D3D3D;
        font-size: 14px;

        .left-tips {
          margin-top: 4px;
          color: #999999;
          font-size: 12px;
          font-weight: 400;
        }
      }
    }

    .good-right {
      color: #999999;
      font-size: 13px;
      display: flex;
      align-items: center;
    }
  }
}

</style>