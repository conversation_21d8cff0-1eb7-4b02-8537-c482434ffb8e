<template>
  <div class="screen-container">
    <div class="tabs-wrap-class">
      <div class="class-tab" v-if="tagList.length > 0">
        <div
            v-for="(item, index) in tagList"
            :key="item.id"
            class="class-tab-item"
            :class="{ 'active-class': activeTag === index }"
            @click="changeTab(index)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="screen-box">
      <div class="screen-tag-container">
        <div class="screen-tag" :class="{'active-tag' : screenNew}" @click="handleScreen('asc')">最早</div>
        <div class="screen-tag" :class="{'active-tag' : !screenNew}" @click="handleScreen('desc')">最新</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref , computed } from "vue";
import { useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();
import {setCache} from "@/utils/cache";
import { useRouter } from "vue-router";
import {jumpLogin, jumpUser} from "@/utils/common.js";
const router = useRouter()

const activeTag = ref(0)
const screenNew = ref(true)

const tagList = ref([
  {name: '全部回复'},
  {name: '只看作者'},
  {name: '我评论的'}
])

const userInfo = computed(() => {
  return authStore.userInfo
})

const props = defineProps({
  list: {
    type: Array,
    default: []
  }
})

const emit = defineEmits(['userChange', 'sort'])



const changeTab = (index) => {
  if(index === 2 &&!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  activeTag.value = index
  emit('userChange', index)
}
const handleScreen = (type) => {
  screenNew.value = !screenNew.value
  emit('sort', type)
}
</script>

<style lang="scss" scoped>
.screen-container {
  padding: 10px 12px 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  position: sticky;
  top: 39px;
  background-color: #FFFFFF;
  .tabs-wrap-class {
    .class-tab {
      display: flex;
      font-size: 10px;
      flex-wrap: nowrap;
      align-items: center;
      white-space: nowrap;
      .class-tab-item {
        padding: 6px 8px;
        margin-right: 10px;
        color: #666666;
        background-color: #F5F5F5;
        border-radius: 5px;
      }
      .active-class {
        color: #478e87;
        background-color: #DFF7F6;
      }
    }

  }
  .screen-box {
    background-color: #F5F5F5;
    border-radius: 12px;
    width: 21vw;
    .screen-tag-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2px;
      .screen-tag {
        font-size: 10px;
        color: #666666;
        padding: 5px 8px;
        border-radius: 12px;
      }
      .active-tag {
        color: #3D3D3D;
        background-color: #FFFFFF;
      }
    }
  }
}
</style>