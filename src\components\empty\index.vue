<template>
  <div class="empty-container">
    <img :src="src" alt="" class="empty" />
    <div class="content" v-if="!hideContent && content">{{ content }}</div>
    <div class="content tips" v-if="!hideContent && tips">{{ tips }}</div>
    <div class="content tips" v-if="!hideContent && tips && tips2">{{ tips2 }}</div>
    <div class="content" v-else-if="!hideContent && !content">{{ text }}</div>
    <slot name="btn"></slot>
  </div>
</template>

<script setup>
import { computed } from "vue";
import emptyIcon from "@/assets/images/empty/emptyIcon.png"
import messageIcon from "@/assets/images/empty/messageIcon.png"

const props = defineProps({
  type: {
    type: String,
    default: 'noContent'
  },
  content: {
    type: String,
    default: ''
  },
  tips: {
    type: String,
    default: ''
  },
  tips2: {
    type: String,
    default: ''
  },
  hideContent: {
    type: Boolean,
    default: false
  }
})


const map = {
  noContent: {
    src: emptyIcon,
    text: '暂无内容',
  },
  noMessage: {
    src: messageIcon,
    text: '哦，暂无此类消息',
  },
  error: {
    src: emptyIcon,
    text: '页面加载错误'
  },
  lostSignal: {
    src: emptyIcon,
    text: '信号丢失',
  },
  train: {
    src: emptyIcon,
    text: '数据表态后可见',
  },
}

const src = computed(() =>{
  return map[props.type]['src']
})

const text = computed(() =>{
  return map[props.type]['text']
})

</script>

<style scoped lang="scss">
.empty-container {
  text-align: center;

  .empty {
    //width: 150px;
    //height: 150px;
    width: 110px;
    height: 110px;
  }

  .content {
    line-height: 21px;
    //margin-top: 12px;
    color: #3D3D3D;
    font-size: 14px;
    text-align: center;
  }
  .tips {
   margin-top: 2px;
  }
}
</style>