<template>
 <div class="equipment-page">
   <div class="title-container">
     <div class="title-left">
       <div class="piece"></div>
       <div>热门装备导购</div>
     </div>
     <div class="title-right">
        <div v-for="(item, index) in list" :key="index">
           <div class="dot" :class="{'active-dot' : activeTab === index}"></div>
        </div>
     </div>
   </div>
   <van-swipe class="news-wrapper" ref="swiper" @change="handleSwitch" :show-indicators="false">
     <van-swipe-item v-for="(item, index) in list" :key="index">
       <div class="content-wrap">
         <div class="goods-container">
            <div class="image"></div>
            <div class="goods-mes">
              <div class="goods-name">商品名称</div>
              <div class="goods-desc">
                <span class="mr-5">55555人想要</span>
                <span>1235人用过</span>
              </div>
              <div class="goods-score">
                <div class="score">9.8</div>
                <div class="score-desc">来自中羽评分5星</div>
              </div>
            </div>
         </div>
         <div class="btn">
           <span class="btn-text">去哪买</span>
         </div>
       </div>
     </van-swipe-item>
   </van-swipe>
 </div>
</template>

<script setup>
import {ref} from "vue";

const activeTab = ref(0)

const list = ref([
  {name: 1},
  {name: 12},
  {name: 13},
  {name: 13},
  {name: 13},
  {name: 13},
])

const handleSwitch = (index) => {
  activeTab.value = index
}
</script>

<style lang="scss" scoped>
.equipment-page {
  padding: 15px 12px 10px;
  background-color: #FFFFFF;
  .title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-left {
      color: #3D3D3D;
      font-weight: 600;
      font-size: 16px;
      display: flex;
      align-items: center;
      .piece {
        width: 6px;
        height: 16px;
        margin-right: 5px;
        background: #478e87;
      }
    }
    .title-right {
      display: flex;
      .dot {
        width: 4px;
        height: 4px;
        margin-right: 5px;
        border-radius: 4px;
        background-color: #D8D8D8;
      }
      .active-dot {
        background-color: #478E87;
      }
    }
  }
  .news-wrapper {
    height: 100px;
    .content-wrap {
      padding-top: 12px;
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      .image {
        width: 80px;
        height: 80px;
        background-color: #D8D8D8;
        border-radius: 5px;
      }
      .goods-container {
        display: flex;
        align-items: center;
        .goods-mes {
          color: #999999;
          font-weight: 400;
          margin-left: 8px;
          .goods-name {
            color: #3D3D3D;
            font-size: 15px;
          }
          .goods-desc {
            padding: 5px 0;
            font-size: 12px;
          }
          .goods-score {
            display: flex;
            align-items: baseline;
            font-size: 10px;
            .score {
              color: #FF5900;
              font-size: 22px;
              margin-right: 5px;
              font-weight: 700;
            }
            .score-desc {
              transform: translateY(-10%);
            }
          }
        }
      }
      .btn {
        transform: translateY(-75%);
        .btn-text {
          color: #FFFFFF;
          font-size: 13px;
          font-weight: 500;
          padding: 5px 15px;
          border-radius: 15px;
          background-color: #FF5900;
        }
      }
    }
  }
}
</style>