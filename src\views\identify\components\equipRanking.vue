<template>
  <div class="eq-ranking">
    <div class="eq-ranking-box">
      <div class="header-box">
        <div class="title">热门求鉴</div>
        <div class="flex-vc more" @click="handleMore">
          <div>更多</div>
          <van-icon name="arrow" size="10"/>
        </div>
      </div>
      <div>
        <div v-for="(item, index) in equipList" :key="index">
          <div class="user-mes-box">
            <div class="user-mes" @click="handleEquip(item.eid)">
              <div class="user-rank-box flex-vhc" :class="`ranking${index+1}`">
                <div class="user-num">{{ index + 1 }}</div>
              </div>
              <van-image
                  class="equip-image"
                  lazy-load
                  :src="`${item?.logo}?imageView2/1/w/200/h/200/q/75/format/webp`"
              />
              <div class="user-desc">
                <div class="flex-vc">
                  <div class="name">{{ item.name }}</div>
                </div>
                <div class="desc-box">
                  <div class="desc">{{ item.desc }}</div>
                </div>
              </div>
            </div>
            <div class="identify-box">
              <div class="identify-num">{{ formatWan(item.count) }}</div>
              <div>求鉴量</div>
            </div>
          </div>
          <van-divider v-if="index < 2" class="line-divider" :style="{borderColor: '#E7E7E7'}"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref} from "vue";
import {formatWan} from "@/utils/common.js";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore()
import {useRouter} from "vue-router";

const router = useRouter()

const props = defineProps({
  equipList: {
    type: Array,
    default: () => []
  },
})


const handleEquip = (eid) => {
  //跳转到装备详情
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/related/index?mag_hide_progress=1`, {
      eid: eid,
    });
  } else {
    router.push({
      name: 'related',
      query: {
        eid: eid,
      }
    })
  }
}

const handleMore = () => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/details/statistics?mag_hide_progress=1`, {activeTab: 4});
  } else {
    router.push({
      name: 'statistics',
      query: {
        activeTab: 4
      }
    })
  }
}

</script>

<style lang="scss" scoped>
.eq-ranking {
  width: 100%;
  border-radius: 10px;
  margin: 0  0 12px;
  background-color: #F5F5F5;
  .eq-ranking-box {
    padding: 12px 12px 0;
  }
  .header-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      color: #3D3D3D;
      font-size: 16px;
    }

    .more {
      color: #999999;
      font-weight: 400;
      font-size: 13px;
    }
  }

  .title-right {
    color: #999999;
    font-size: 13px;
    font-weight: 400;
  }

  .user-mes-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 11px 0;

    .user-mes {
      display: flex;
      align-items: center;

      .user-rank-box {
        width: 24px;
        height: 24px;
        margin-right: 6px;
        background-image: url("@/assets/images/details/ranking4.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        .user-num {
          color: #FFFFFF;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .ranking1 {
        background-image: url("@/assets/images/details/ranking1.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }

      .ranking2 {
        background-image: url("@/assets/images/details/ranking2.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }

      .ranking3 {
        background-image: url("@/assets/images/details/ranking3.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        overflow: hidden;
        border-radius: 26px;
        margin-right: 8px;
      }

      .equip-image {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        overflow: hidden;
        border-radius: 8px;
        margin-right: 8px;
        border: 1px solid #F5F5F5;
      }

      .user-desc {

        .name {
          color: #3D3D3D;
          font-size: 14px;
          width: 186px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .ratio {
          color: #FA5151;
          font-size: 10px;
          padding: 2px 3px;
          margin-bottom: 2px;
          border-radius: 3px;
          margin-left: 5px;
          background: #FFEEEE;
        }

        .count-box {
          margin-top: 5px;
          display: flex;
          align-items: center;
          font-weight: 600;
          font-size: 11px;
          color: #999999;
        }

        .desc-box {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 10px;
          margin-top: 2px;

          .tag {
            color: #FFFFFF;
            padding: 2px 4px;
            border-radius: 3px;
            background-color: #819FFF;
            margin-right: 5px;
          }

          .pink {
            background-color: #FF7CAA;
          }

          .purple {
            background-color: #819FFF;
          }

          .orange {
            background-color: #FF8F1F;
          }

          .golden {
            color: #3D3D3D;
            font-weight: 500;
            border: 1px solid #FFE3B9;
            background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
          }

          .green {
            background-color: #00AC72;
          }

          .desc {
            padding-top: 3px;
            color: #999999;
            width: 186px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .identify-box {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      color: #A8A8A8;
      font-size: 10px;
      font-weight: 400;

      .identify-num {
        color: #478E87;
        font-size: 17px;
        font-weight: 700;
        padding-bottom: 3px;
      }
    }
  }

  .line-divider {
    ::v-deep.van-divider {
      margin: 0;
      padding-left: 78px;
    }
  }
}
</style>