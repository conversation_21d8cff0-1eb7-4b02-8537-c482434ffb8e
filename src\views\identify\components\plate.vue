<template>
 <div class="plate-container">
   <div class="plate-box" v-for="(item, index) in plateList" :key="index">
     <div class="plate-item" :class="{'blue-item': index === 1}" @click="handleJump(item)">
       <div class="plate-left">
         <img class="item-icon" :src="item.icon" />
       </div>
       <div class="plate-right">
         <div class="right-top">
           <div>{{item.name}}</div>
           <img class="right-icon" :src="rightIcon" />
         </div>
         <div class="right-bot">{{item.desc}}</div>
       </div>
     </div>
   </div>
 </div>
</template>

<script setup>
import { ref } from 'vue'
import rightIcon from '@/assets/images/common/right.png'
import bookIcon from '@/assets/images/identify/bookIcon.png'
import messageIcon from '@/assets/images/identify/messageIcon.png'
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();

const plateList = ref([
  {
    name: '鉴定好文',
    path: 'https://www.badmintoncn.com/thread.php?a=list&classid=35&mag_hide_progress=1',
    desc: '了解更多鉴定知识',
    icon: bookIcon,
  },
  {
    name: '鉴定交流',
    path: 'cboapp://forumThreadList?fid=82&circleId=114',
    desc: '了解更多关于鉴定',
    icon: messageIcon,
  },
])


const handleJump = (item) => {
  if(!authStore.phone) {
    if(item.name === '鉴定好文') {
      window.location.href = item.path;
    } else {
      alert('请下载中羽在线APP使用')
    }
  } else {
    window.mag.newWin(item.path)
  }
}

</script>

<style lang="scss" scoped>
.plate-container {
  display: flex;
  justify-content: space-between;
  margin: 0 12px 13px;
 .plate-box {
   .plate-item {
     display: flex;
     align-items: center;
     color: #3D3D3D;
     padding: 9px 23px 9px 12px;
     font-size: 14px;
     border-radius: 15px;
     background-color: #FFF6E1;
     .plate-left {
       .item-icon {
         width: 36px;
         height: 36px;
         padding-right: 6px;
       }
     }
     .plate-right {
       .right-top {
         display: flex;
         align-items: center;
         .right-icon {
           width: 4px;
           height: 8px;
           padding-left: 6px;
         }
       }
       .right-bot {
         color: #999999;
         font-size: 12px;
         font-weight: 400;
         padding-top: 4px;
       }
     }
   }
   .blue-item {
     background: #F0F6FF;
   }
 }
}
</style>