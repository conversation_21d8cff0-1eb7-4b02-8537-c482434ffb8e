import {defineStore} from "pinia"
import {computed, ref} from "vue";

function isEmptyObject(obj) {
    return obj && typeof obj === 'object' && Object.keys(obj).length === 0;
}

export const useBriefStore = defineStore('brief', () => {
    const searchInfo = ref({
        categoryKey: -1
    })
    const indexSearch = ref({
        categoryKey: -1
    })
    const screenInfo = computed(() => {
        if(!isEmptyObject(searchInfo.value)) {
            return searchInfo.value
        } else {
            return indexSearch.value
        }
    })
    return { searchInfo, indexSearch, screenInfo }
}, {
    persist: true,
})

