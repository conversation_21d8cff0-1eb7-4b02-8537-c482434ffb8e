<template>
  <van-popup
      v-if="equipmentShow"
      v-model:show="equipmentShow"
      position="bottom"
      round
      :style="{ height: '80%' }"
  >
    <div class="equ-page">
      <div class="top-box">
        <div class="title-container">
          <div></div>
          <div>关联装备</div>
          <img class="close-icon" :src="closeIcon" @click="hide"/>
        </div>
        <div class="search-container">
          <van-search v-model="keyword" placeholder="搜索关键词..." shape="round" @update:model-value="handleChange">
            <template #left-icon>
              <img class="search-icon" :src="searchIcon">
            </template>
          </van-search>
        </div>
      </div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list mb-80">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            :finished-text="finishedText"
            @load="onLoad"
            ref="listRef"
            :error.sync="errorStatus"
            :immediate-check="false"
        >
          <div class="equip-container">
            <div v-if="equList?.length" v-for="item in equList">
              <div class="equip-box">
                <div class="equip-left" @click="jumpEquip(item.id)">
                  <van-image class="image-box" fit="cover" lazy-load
                             :src="`${item.pic}?imageView2/1/w/200/h/200/q/75/format/webp`"/>
                  <div class="equip-mes">
                    <div class="equip-name">{{ `${item.name}` }}</div>
                    <div class="equip-desc">
                      <div>{{ item?.keys }}</div>
                      <!--                      <div>{{ `${formatWan(item.want)}人想要` }}</div>-->
                      <!--                      <div class="ml-5">{{ `${formatWan(item.used)}用过` }}</div>-->
                      <!--                      <div class="ml-5">{{ `${formatWan(item.score)}评分` }}</div>-->
                    </div>
                  </div>
                </div>
                <div class="equip-right" @click="handleAdd(item)">
                  <img class="equip-icon" :src="equipObj.equipId === item.id ? checkedIcon : unCheckedIcon"/>
                </div>
              </div>
              <van-divider class="line-divider" :style="{borderColor: 'f5f5f5'}"/>
            </div>
            <Empty v-if="!equList?.length" class="empty-wrap" content="抱歉！暂无相关数据">
              <template #btn>
                <div v-if="Number(cateId) !== 0 && Number(brandId) !== 0" class="find-btn" @click="cannotFind">
                  <span class="find-text">找不到该装备</span>
                </div>
              </template>
            </Empty>
          </div>
        </van-list>
      </van-pull-refresh>
      <div class="footer-box" :class="{'ios-footer': IS_IOS}">
        <div class="footer-btn left-btn" @click="equipmentShow = false">取消</div>
        <div class="footer-btn right-btn" @click="handleConfirm">确定</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {computed, nextTick, ref} from "vue";
import {debounce} from "lodash";
import closeIcon from '@/assets/images/common/closeIcon.png'
import searchIcon from "@/assets/images/identify/searchIcon.png"
import checkedIcon from "@/assets/images/identify/checkedIcon.png"
import unCheckedIcon from "@/assets/images/identify/unCheckedIcon.png"
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
import {isIOS} from "@/utils/common.js";


const props = defineProps({
  brandId: {
    type: [String, Number],
    default: 0
  },
  cateId: {
    type: [String, Number],
    default: 0
  }
})

const emits = defineEmits(['addEquip'])


const authStore = useAuthStore();
const IS_IOS = isIOS()
const keyword = ref()
const brandId = ref()
const cateId = ref()
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const listRef = ref()
const finishedText = ref('没有更多了')
const errorStatus = ref(false)
const equipmentShow = ref(false)
const equipObj = ref({
  equipId: '',
  equipName: '',
})
const paramsSize = ref({
  page: 1,
  pageSize: 10,
})
const equList = ref([])


const userInfo = computed(() => {
  return authStore.userInfo
})


const onRefresh = () => {
  paramsSize.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    handleSearch();
  })
}

const onLoad = () => {
  if (equList.value) {
    paramsSize.value.page++
    nextTick(() => {
      handleSearch();
    })
  }
};


const jumpEquip = (eid) => {
  if (!authStore.phone) {
    window.location.href = `https://www.badmintoncn.com/eqm.php?a=view&eid=${eid}`;
  } else {
    window.mag.newWin(`https://www.badmintoncn.com/eqm.php?a=view&eid=${eid}&mag_hide_progress=1&mag_sapp_style=1&themecolor=30BC9D`)
  }
}

const handleAdd = (item) => {
  equipObj.value.equipId = item.id
  equipObj.value = {
    equipId: item.id,
    equipName: item.name
  }
}

const cannotFind = () => {
  emits('cannotFind')
  equipmentShow.value = false
}


//处理数字超过1w的情况
const formatWan = (num) => {
  if (typeof num !== 'number' || isNaN(num)) {
    return '0';
  }
  if (num >= 10000) {
    return (num / 10000).toFixed(1).replace(/\.0$/, '') + 'W';
  } else {
    return num.toString();
  }
}


const handleChange = debounce(async () => {
  equList.value = []
  paramsSize.value.page = 1
  paramsSize.value.pageSize = 10
  await handleSearch()
}, 400)


const handleSearch = async () => {
  loading.value = true;
  try {
    const res = await identifyApi.getEquipList({
      ...paramsSize.value,
      brand: brandId.value,
      cate: cateId.value,
      eid: equipObj.value.equipId ? equipObj.value.equipId : '',
      keyword: keyword.value
    })
    if (res.code === 200) {
      if (refreshing.value) {
        equList.value = [];
        refreshing.value = false;
        finished.value = false;
      }

      const {list = [], total = 0} = res.data || {};
      if (list) {
        equList.value = equList.value.concat(list);
      } else {
        equList.value = []
      }
      finished.value = total <= equList.value.length || list === null;
      finishedText.value = equList.value.length === 0 ? "" : "没有更多了";
    } else {
      errorStatus.value = true;
    }
  } catch (error) {
    errorStatus.value = true;
  } finally {
    loading.value = false;
  }
}


const handleConfirm = () => {
  emits('addEquip', equipObj.value)
  hide()
}


const open = async (obj, id) => {
  paramsSize.value.page = 1
  keyword.value = obj.keyword
  cateId.value = obj.cateId
  brandId.value = obj.brandId
  equipObj.value.equipId = id
  equList.value = []
  await handleSearch()
  equipmentShow.value = true
}

const hide = () => {
  equipmentShow.value = false
}

defineExpose({open, hide});
</script>

<style lang="scss" scoped>
.equ-page {
  .top-box {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: #FFFFFF;
  }

  .title-container {
    padding: 18px 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;
    font-weight: 600;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .search-container {
    padding: 0 12px 15px;

    .search-icon {
      height: 14px;
      width: 14px;
      margin-top: 6px;
    }

    .van-search {
      padding: 0;
    }
  }

  .equip-container {
    padding: 0 12px;

    .equip-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .equip-left {
        display: flex;
        //align-items: center;

        .image-box {
          width: 60px;
          height: 60px;
          border-radius: 5px;
          overflow: hidden;
          box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.1);
        }

        .equip-mes {
          margin-left: 10px;
          display: flex;
          line-height: 1.2;
          flex-direction: column;
          justify-content: space-around;
          color: #3D3D3D;
          font-size: 16px;
          font-weight: 400;

          .equip-desc {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #999999;
            font-size: 10px;
          }
        }
      }

      .equip-right {
        flex-shrink: 0;
        margin-left: 6px;

        .equip-icon {
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  .footer-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #FFFFFF;

    .footer-btn {
      text-align: center;
      width: 168px;
      height: 44px;
      line-height: 44px;
      font-size: 16px;
      border-radius: 22px;
      color: #478E87;
      background-color: #DFF7F6;
    }

    .right-btn {
      color: #FFFFFF;
      background-color: #478E87;
    }
  }

  .ios-footer {
    padding: 12px 12px 26px;
  }

  .van-divider {
    margin: 11px 0;
  }
}

.empty-wrap {
  margin-top: 25%;
}

.find-btn {
  margin-bottom: 20px;

  .find-text {
    color: #478E87;
    font-size: 14px;
    font-weight: 400;
    border-radius: 20px;
    padding: 12px 20px 10px;
    border: 1px solid #93C3BF;
    background-color: #DFF7F6;
  }
}

::v-deep.van-popup {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
  /* 隐藏滚动条 - Webkit浏览器 (Chrome, Safari, Edge等) */
  &::-webkit-scrollbar {
    display: none !important;
    width: 0;
    height: 0;
  }
}
</style>