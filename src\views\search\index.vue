<template>
<div class="search-page">
 <div class="top" style="height: 100px; background-color: red"></div>
 <div class="center" style="height: 200px; background-color: pink"></div>
 <div class="footer" style="height: 100px; background-color: #9c26b0"></div>
</div>
</template>

<script setup>
</script>

<style scoped lang="scss">
.search-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  .top,
  .footer {
    width: 100%;
    flex-shrink: 0;
  }
  .center {
    width: 100%;
    flex: 1;
    overflow: auto;
  }
}
</style>