<template>
  <div class="opinion-page" :class="{'opinion-page-red': info?.red}">
    <div v-if="info?.red" class="bg-box"
         :class="[info?.redTime > 0 ? 'bg-img' : (info?.redTime === 0 && info?.redAmountMine === 0 && (info.appraisal !== null && info?.redQ && info?.redClaimMine) && Number(userInfo.id) !== Number(info?.user?.uid)) ? 'bg-grey' : '' ]">
      <div v-if="info?.redTime > 0" class="count-down-box">
        <van-count-down :time="info?.redTime * 1000" format="HH 时 mm 分 ss 秒" @finish="handleFinish">
          <template #default="timeData">
            <div class="flex flex-vc">
              <div class="block">{{ formatNumber(timeData.days * 24 + timeData.hours) }}</div>
              <div class="block">{{ formatNumber(timeData.minutes) }}</div>
              <div class="block">{{ formatNumber(timeData.seconds) }}</div>
            </div>
          </template>
        </van-count-down>
        <div v-if="!oneself" class="tips">期间内表态有机会获得随机红包</div>
        <div v-if="oneself" class="tips">期间内鉴定者表态有机会获得红包</div>
      </div>
      <div
          v-if="info?.redTime <= 0 && !oneself && info?.appraisalPermission && info?.appraisal !== null && !info?.redClaimMine && info?.redQ"
          class="lottery-box">
        <div class="lottery-text">抽奖中，拼手气抽取随机红包奖励</div>
        <div class="lottery-btn" @click="handleLottery">抽奖
          <img class="hb-img" :src="bhbIcon"/>
        </div>
      </div>
      <div
          v-if="info?.redTime === 0 && !oneself && info?.appraisalPermission && info?.redAmountMine"
          class="lottery-box">
        <div class="lottery-text">{{ `已开奖，本次鉴定获得随机红包¥${Number(info?.redAmountMine).toFixed(2)}` }}</div>
        <div class="draw-btn" @click="handleMyWallet">钱包
          <img class="hb-img" :src="walletIcon"/>
        </div>
      </div>
      <div v-if="info?.redTime <= 0  && oneself" class="lottery-draw-box">
        <div class="draw-text" v-if="info?.redNumAll > info?.redNumClaim && totalPeople < info?.redNumAll">
          {{ `已开奖，被领${info?.redNumClaim}/${info?.redNumAll}个红包，未达预期系统持续推广中` }}
        </div>
        <div class="draw-text" v-if="info?.redNumClaim >= info?.redNumAll || totalPeople >= info?.redNumAll">
          {{ `已开奖，被领${info?.redNumClaim}/${info?.redNumAll}个红包，参与人数已达求鉴预期` }}
        </div>

      </div>
      <div class="lottery-draw-box">
        <div
            v-if="(!oneself && !info?.appraisalPermission && info?.redTime === 0) || (!oneself && info?.redTime === 0 && !info?.redQ)"
            class="draw-text">
<!--          {{ `已结束，已有${info?.redNumClaim}位球友获得随机红包奖励` }}-->
          {{ `红包活动已结束，满足奖励规则后请下次及时参与` }}
        </div>
        <div v-if="!oneself && info?.redAmountMine === 0 && info.appraisal !== null && info?.redQ"
             class="not-winner-box">
          <div class="not-text">抱歉，本次鉴定您未抽中红包，祝下次好运</div>
        </div>
      </div>
    </div>
    <div class="opinion-box" :class="{'is-red-box' : info?.red}">
      <div class="title">
        <div class="flex-vc">
          <div v-if="info.appraisal === null">
            {{ Number(userInfo.id) !== Number(info?.user?.uid) ? '选择鉴定看法' : '我的求鉴' }}
          </div>
          <div v-if="info.appraisal" class="title-true">{{
              info.appraisalRegretted ? '您已修改表态：看正' : '您已表态：看正'
            }}
          </div>
          <div v-if="!info.appraisal && info.appraisal !== null" class="title-false">
            {{ info.appraisalRegretted ? '您已修改表态：看假' : '您已表态：看假' }}
          </div>
          <div v-if="info?.train" class="train-tag">训</div>
          <div v-if="!info?.train && info?.regret" class="modify-box" @click="handleModify">
            <img class="modify-icon" :src="editIcon"/>
            <div>可修改</div>
          </div>
          <div v-if="info?.red" class="reward-rules" @click="changeRule">奖励规则
            <img class="doubt-icon" :src="doubtIcon"/></div>
        </div>
        <div v-if="userMes?.train && info?.appraisal === null && Number(userInfo.id) !== Number(info?.user?.uid)">
          {{ `表态后可见` }}
        </div>
        <div v-if="(!userMes?.train || info?.appraisal !== null) || Number(userInfo.id) === Number(info?.user?.uid)">
          {{ `${totalPeople}人参与` }}
        </div>
      </div>
      <div class="ratio-container">
        <div class="left-box flex-vc"
             :style="{ width: totalPeople === 0 ? '50%' :  realPercentage === 0  ? 30 + '%' : realPercentage + '%' }">
          <div class="left-true flex-vhc">正</div>
          <div class="left-num">{{ realPercentage }}%</div>
        </div>
        <img class="bias-icon" :src="biasIcon" :style="{ left: calcIconPosition() }"/>
        <div class="right-box"
             :style="{ width: totalPeople === 0 ? '50%' : fakePercentage === 0 ? 30 + '%' : fakePercentage + '%' }">
          <div class="right-num">{{ fakePercentage }}%</div>
          <div class="right-true flex-vhc">假</div>
        </div>
      </div>
      <div
          v-if="(userMes?.train && info?.appraisal !== null) || !userMes?.train || Number(userInfo.id) === Number(info?.user?.uid)"
          style="display: flex; align-items: center; justify-content: space-between">
        <UserGroup :avatars="realAvatars" @click="handleRanking(1)"/>
        <UserGroup :true-show="false" :avatars="fakeAvatars" @click="handleRanking(2)"/>
      </div>
      <!--      <div-->
      <!--          v-if="(totalPeople === 0 && !userMes?.train) || (Number(userInfo.id) === Number(info?.user?.uid) && totalPeople === 0)"-->
      <!--          class="not-have"> —— 暂无球友表态 ——-->
      <!--      </div>  -->
      <div
          v-if="(Number(userInfo.id) === Number(info?.user?.uid) && totalPeople === 0) || (totalPeople === 0 && info.redTime === 0 && !userMes?.train) "
          class="not-have"> —— 暂无球友表态 ——
      </div>
      <div class="not-have"
           v-if="Number(userInfo.id) !== Number(info?.user?.uid) && info.redTime > 0 &&info?.appraisalPermission && info?.appraisal === null && !userMes?.train">
        ——
        鉴定结果表态后可见 ——
      </div>
      <div class="not-have"
           v-if="Number(userInfo.id) !== Number(info?.user?.uid) && !info?.appraisalPermission && info?.redTime > 0"> ——
        您当前不可参与 ——
      </div>
      <div class="rule-box"></div>
      <div
          v-if="userInfo.groupId < 12 && userInfo.groupId !== 1 && userInfo.groupId !== 2 && userInfo.groupId !== 3 && !userMes?.train && Number(userInfo.id) !== Number(info?.user?.uid) && info?.appraisal === null && !info?.red"
          class="not-have">温馨提示：您所在用户组无表态权限，
        <text style="color: #5070D6" @click="handleOpen">开启训练</text>
      </div>
      <div
          v-if="((!userInfo.groupId || !userInfo.id) && !info?.red) || (!info?.appraisalPermission && Number(userInfo.id) !== Number(info?.user?.uid) && info?.redTime === 0 && info?.red && userInfo.groupId < 12 && userInfo.groupId !== 1 && userInfo.groupId !== 2 && userInfo.groupId !== 3)"
          class="not-have">温馨提示：您所在用户组无表态权限
      </div>
      <div v-if="userMes?.train && info?.appraisal === null && Number(userInfo.id) !== Number(info?.user?.uid)"
           class="not-have pt-16">温馨提示：训练中，数据表态后可见
      </div>
      <div
          v-if="!info?.appraisalPermission && Number(userInfo.id) !== Number(info?.user?.uid) && info?.red && info?.redTime > 0"
          class="not-rule">
        <span class="not-rule-text">温馨提示：满足奖励规则即可参与，<text style="color: #5070D6"
                                                                        @click="changeRule">查看规则</text></span>
      </div>
      <div v-if="!info?.red ">
        <div class="btn-box"
             v-if="userInfo.id && Number(userInfo.id) !== Number(info?.user?.uid) && info?.appraisal === null && ((userInfo.groupId >= 12 || userInfo.groupId === 1 || userInfo.groupId === 2 || userInfo.groupId === 3) || userMes?.train)">
          <div class="btn" @click="handleOpinion(1)"><img class="btn left-btn" :src="greenBtn"/>
            <div class="btn-text">看正</div>
          </div>
          <div class="btn" @click="handleOpinion(2)"><img class="btn right-btn" :src="redBtn"/>
            <div class="btn-text">看假</div>
          </div>
        </div>
      </div>
      <div v-if="info?.red && userInfo.id && Number(userInfo.id) !== Number(info?.user?.uid)">
        <div class="btn-box"
             v-if="info?.redTime > 0 && info?.appraisalPermission && info?.appraisal === null || info?.redTime <= 0 && userInfo.id && Number(userInfo.id) !== Number(info?.user?.uid) && info?.appraisal === null && ((userInfo.groupId >= 12 || userInfo.groupId === 1 || userInfo.groupId === 2 || userInfo.groupId === 3) || userMes?.train) ">
          <div class="btn" @click="handleOpinion(1)"><img class="btn left-btn" :src="greenBtn"/>
            <div class="btn-text">看正</div>
          </div>
          <div class="btn" @click="handleOpinion(2)"><img class="btn right-btn" :src="redBtn"/>
            <div class="btn-text">看假</div>
          </div>
        </div>
      </div>
    </div>
    <RewardRule ref="rewardRuleRef"/>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from "vue";
import UserGroup from "@/views/identify/details/components/userGroup.vue";
import RewardRule from "@/views/identify/components/rewardRule.vue";
import avatarIcon from "@/assets/images/common/avatar.png"
import biasIcon from "@/assets/images/details/biasIcon.png"
import editIcon from "@/assets/images/common/greyEditicon.png"
import doubtIcon from "@/assets/images/reward/doubt.png"
import greenBtn from "@/assets/images/details/greenBtn.png"
import redBtn from "@/assets/images/details/redBtn.png"
import bhbIcon from "@/assets/images/reward/bhb.png"
import walletIcon from "@/assets/images/reward/wallet.png"
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore()


const userInfo = computed(() => {
  return authStore.userInfo
})


const time = ref(4 * 60 * 60 * 1000);
const totalPeople = ref(0)
const positiveVotes = ref(0);
const negativeVotes = ref(0);
const realAvatars = ref([])
const fakeAvatars = ref([])
const rewardRuleRef = ref()
const topBg = ref(1)
// const oneself = ref(true)


const props = defineProps({
  info: {
    type: Object,
    default: () => {
    }
  },
  userMes: {
    type: Object,
    default: () => {
    }
  },
  tid: {
    type: Number,
    default: 0
  }
})

const oneself = userInfo.value.id === props.info?.user?.uid
const hasData = computed(() => totalPeople.value > 0);


const formatNumber = (num) => {
  return num.toString().padStart(2, '0');
};

const emit = defineEmits(['ranking', 'opinion', 'modify', 'eaTotal', 'openAttr', 'lottery'])


const changeRule = () => {
  rewardRuleRef.value.show()
}

//点击抽奖按钮
const handleLottery = () => {
  emit('lottery')
}

//跳转到钱包页面
const handleMyWallet = () => {
  if (authStore.phone) {
    window.mag.newWin(`/mag/user/v1/user/wallet`);
  } else {
    showToast('请到中羽在线APP钱包中查看')
  }
}

//倒计时结束
const handleFinish = () => {
  props.info.redTime = 0
  console.log('倒计时结束')
}

const calcIconPosition = () => {
  if (!hasData.value) return '43%';
  if (realPercentage.value === 0) return '17%';
  if (fakePercentage.value === 0) return '69%';
  if (realPercentage.value === fakePercentage.value) return '43%';
  let newRealPercentage = realPercentage.value - 8
  return newRealPercentage > 69 ? '69%' : newRealPercentage < 18 ? '18%' : `${newRealPercentage}%`
  // return realPercentage.value > fakePercentage.value
  //     ? `${realPercentage.value - 17}%`
  //     : `${realPercentage.value + 2}%`;
}

const realPercentage = computed(() => {
  if (!totalPeople.value) return 0
  return Math.round((positiveVotes.value / totalPeople.value) * 100);
})

const fakePercentage = computed(() => {
  if (!totalPeople.value) return 0
  return Math.round((negativeVotes.value / totalPeople.value) * 100);
})

const handleOpen = () => {
  emit('openAttr')
}


const handleRanking = (type) => {
  emit('ranking', type)
}

const handleOpinion = (type) => {
  emit('opinion', type)
}

const handleModify = () => {
  emit('modify',)
}

const getList = async () => {
  let res = await identifyApi.getAppraisal({
    tid: props.tid,
    page: 1,
    pageSize: 10
  })
  if (res.code === 200) {
    let data = res.data
    if (data.list) {
      positiveVotes.value = ((props.userMes?.train && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id) || (props.info?.red && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id && props?.info?.redTime > 0)) ? 0 : data.list.real.total
      negativeVotes.value = ((props.userMes?.train && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id) || (props.info?.red && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id && props?.info?.redTime > 0)) ? 0 : data.list.fake.total
      totalPeople.value = negativeVotes.value + positiveVotes.value
      realAvatars.value = ((props.userMes?.train && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id) || (props.info?.red && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id && props?.info?.redTime > 0)) ? 0 : data.list?.real?.list
      fakeAvatars.value = ((props.userMes?.train && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id) || (props.info?.red && props.info.appraisal === null && props.info?.user?.uid !== userInfo.value.id && props?.info?.redTime > 0)) ? 0 : data.list?.fake?.list
      // realAvatars.value = data.list?.real?.list
      // fakeAvatars.value = data.list?.fake?.list
      emit('eaTotal', totalPeople.value)
    }

  }
}

onMounted(() => {
  getList()
})

defineExpose({getList})

</script>

<style scoped lang="scss">
.opinion-page {
  margin: 20px 12px;

  .bg-box {
    position: relative;
    z-index: 1;
    width: 351px;
    height: 82px;
    border-radius: 10px;
    background-color: #FFEBE5;
  }

  .bg-grey {
    background-color: #EDEDED;
  }

  .bg-img {
    background-image: url("@/assets/images/reward/tpbg.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .count-down-box {
    padding: 11px 12px;
    display: flex;
    align-items: center;

    .block {
      margin-right: 5px;
      color: #FF3232;
      font-size: 12px;
      font-weight: 600;
      border-radius: 3px;
      //padding: 4px 3px;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      flex-shrink: 0;
      background-color: #FFFFFF;
    }

    .tips {
      color: #FFFFFF;
      font-size: 13px;
    }
  }

  .lottery-box {
    padding: 11px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .lottery-text {
      font-size: 13px;
      color: #FF3700;
    }

    .lottery-btn {
      padding: 5px 8px;
      color: #FFFFFF;
      font-size: 13px;
      border-radius: 7px;
      background-color: #FF3700;

      .hb-img {
        width: 12px;
        height: 12px;
      }
    }

    .draw-btn {
      padding: 3px 8px;
      color: #FF3700;
      font-size: 13px;
      border-radius: 7px;
      background-color: #FFFFFF;
      border: 1px solid #FFD4CB;

      .hb-img {
        width: 12px;
        height: 12px;
      }
    }
  }

  .lottery-draw-box {
    padding: 15px 12px;

    .draw-text {
      font-size: 13px;
      color: #FF3700;
    }

    .not-text {
      font-size: 13px;
      color: #999999;
    }
  }

  //.not-winner-box {
  //  padding: 15px 12px;
  //
  //
  //}

  .opinion-box {
    position: relative;
    z-index: 5;
    color: #3D3D3D;
    border-radius: 10px;
    background-color: #F5F5F5;
    border-top: 1px solid #FFFFFF;

    .title {
      font-size: 13px;
      font-weight: 600;
      padding: 12px 12px 11px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .title-true {
      font-weight: 600;
      color: #00B578;
    }

    .title-false {
      font-weight: 600;
      color: #FA5151;
    }

    .train-tag {
      color: #FFFFFF;
      font-size: 10px;
      font-weight: 600;
      padding: 3px;
      border-radius: 5px;
      margin-left: 5px;
      background-color: #9673FF;
    }

    .modify-box {
      display: flex;
      align-items: center;
      margin-left: 8px;
      padding: 2px 3px;
      font-size: 10px;
      font-weight: 600;
      border-radius: 5px;
      background-color: #FFF64F;

      .modify-icon {
        width: 12px;
        height: 12px;
        margin-right: 2px;
      }
    }

    .reward-rules {
      display: flex;
      align-items: center;
      color: #999999;
      font-size: 13px;
      font-weight: 400;
      margin-left: 5px;

      .doubt-icon {
        width: 12px;
        height: 12px;
        margin-left: 5px;
      }
    }

    .ratio-container {
      font-weight: 600;
      display: flex;
      width: calc(100% - 24px);
      position: relative;
      margin: 0 12px;
      font-weight: 600;
      box-sizing: border-box;

      .left-box {
        min-width: 24%;
        height: 36px;
        background-color: #D1EEE4;
        border-top-left-radius: 20px;
        border-bottom-left-radius: 20px;

        .left-true {
          color: #00B578;
          font-size: 16px;
          font-weight: 600;
          width: 26px;
          height: 26px;
          margin: 5px;
          border-radius: 50%;
          background-color: #FFFFFF;
        }

        .left-num {
          color: #00B578;
          font-size: 13px;
        }
      }

      .bias-icon {
        position: absolute;
        left: 68%;
        width: 45px;
        height: 36px;
      }

      .right-box {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        min-width: 24%;
        height: 36px;
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px;
        background-color: #FAE0E0;

        .right-true {
          color: #FA5151;
          font-size: 16px;
          font-weight: 600;
          width: 26px;
          height: 26px;
          margin: 5px;
          border-radius: 50%;
          background-color: #FFFFFF;
        }

        .right-num {
          color: #FA5151;
          font-size: 13px;
        }
      }
    }

    .not-have {
      color: #999999;
      font-size: 13px;
      font-weight: 400;
      text-align: center;
      padding-top: 5px;
      padding-bottom: 20px;
    }

    .not-rule {
      display: flex;
      justify-content: center;
      align-items: center;

      .not-rule-text {
        color: #999999;
        font-size: 13px;
        padding: 12px 33px;
        border-radius: 5px;
        margin-bottom: 15px;
        background-color: #EDEDED;
      }
    }

    .btn-box {
      padding: 0 12px 15px;
      display: flex;

      .btn {
        width: 163px;
        height: 36px;
        position: relative;
      }

      .btn-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -40%);
        color: #FFFFFF;
        font-size: 16px;
        font-weight: 600;
      }
    }

  }

  .is-red-box {
    margin-top: -40px;
  }
}

.opinion-page-red {
  margin: 20px 12px;
}
</style>