import {defineStore} from "pinia"

export const useAuthStore = defineStore('auth', {
    state: () => ({
        token: '',
        tokenInfo: {},
        deviceToken: '',
        firstFlag: false,
        // searchInfo: {},
        userInfo: {},
        // apiUrl: 'http://192.168.31.146:3000',
        apiUrl: 'https://www.badmintoncn.com/cbo_ea/dist/index.html#',
        // apiUrl: 'https://www.badmintoncn.com/cbo_ea_dev/dist/index.html#',
        phone: window.navigator.appVersion.toLowerCase().indexOf('magappx') != -1,
        IS_IOS: false,
        horizontal: true,
        switchType: false,
        replyIcon: true,
        CT_APP_WAP_DOMAIN: 'https://m2.badmintoncn.com',
        CT_HOME_URL: 'https://www.badmintoncn.com',
        CT_APP_BBS_DOMAIN: 'https://bbs.badmintoncn.com',
        CT_LOGIN_URL: 'https://bbs.badmintoncn.com/member.php?mod=logging&action=login'
    }),
    persist: true,
})