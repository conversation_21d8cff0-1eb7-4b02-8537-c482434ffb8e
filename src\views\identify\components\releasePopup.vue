<template>
  <van-popup
      v-if="releaseShow"
      v-model:show="releaseShow"
      round
      position="bottom"
  >
    <div class="release-popup">
      <div class="title-box">
        <div></div>
        <div>选择求鉴信息</div>
        <img class="close-icon" :src="closeIcon" @click="hide"/>
      </div>
      <div class="tips mt-8 flex-col-hc">温馨提示：按照步骤完善信息有助于更快鉴定</div>
      <div class="classify">
        <TitleTop :right="false">
          <template #title>
            <div>我要求鉴<span style="color: #FA5151;">*</span></div>
          </template>
        </TitleTop>
      </div>
      <div class="tag-container">
        <template v-for="(item, index) in categoryList" :key="index">
          <div class="tag-item" :class="{'active-item' : activeCategory === item.name}" @click="handleCategory(item)">
            {{ item.name }}
            <div v-if="activeCategory === item.name" class="check-icon">
              <img class="check-img" :src="answerIcon"/>
            </div>
          </div>
        </template>
      </div>
      <div class="classify pt-30">
        <TitleTop :right="false">
          <template #title>
            <div>所属品牌<span style="color: #FA5151;">*</span></div>
          </template>
        </TitleTop>
      </div>
      <div class="tag-container">
        <template v-for="(item, index) in tagList" :key="index">
          <div class="tag-item" :class="{'active-item' : activeTagId === item.id}" @click="handleTag(item)">
            {{ item.name }}
            <div v-if="activeTagId === item.id" class="check-icon">
              <img class="check-img" :src="answerIcon"/>
            </div>
          </div>
        </template>
      </div>
      <div class="footer-box">
        <div class="btn left-btn" @click="handleCancel">暂不发布</div>
        <div class="btn right-btn" @click="handleSave">下一步</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref} from "vue";
import TitleTop from "@/components/titleTop.vue";
import closeIcon from '@/assets/images/common/closeIcon.png'
import answerIcon from "@/assets/images/common/answerIcon.png"
import {getCache} from "@/utils/cache.js";
import {commonCategory} from "@/views/identify/lists.js";

const releaseShow = ref(false)
const activeTagName = ref('')
const activeTagId = ref('')
const activeCategory = ref('')
const activeCategoryId = ref('')
const activeLogo = ref('')

const tagList = ref([])

const categoryList = commonCategory.value.slice(1)

const handleTag = (item) => {
  activeTagName.value = item.name
  activeTagId.value = item.id
  activeLogo.value = item.logo
}

const handleCategory = (item) => {
  activeCategory.value = item.name
  activeCategoryId.value = item.id
}

const emit = defineEmits(['save'])

//保存
const handleSave = () => {
  if (activeCategory.value && activeTagName.value) {
    hide()
    let item = {
      category: activeCategory.value,
      categoryId: activeCategoryId.value,
      brand: activeTagName.value,
      brandId: activeTagId.value,
      // logo: activeLogo.value
    }
    emit('save', item)
    // showToast('已保存属性')
  } else {
    showToast('请选择品牌及品类')
  }
}

const handleCancel = () => {
  hide()
}

const showList = () => {
  // let cateList = JSON.parse(getCache('CATEGORY_LIST'))
  // categoryList.value = cateList.slice(1)

  let brand = JSON.parse(getCache('BRAND_LIST'))
  tagList.value = brand.slice(1)
}

const show = (obj) => {
  showList()
  activeTagName.value = obj.name
  activeTagId.value = obj.id
  activeCategory.value = ''
  activeCategoryId.value = ''
  activeLogo.value = obj.logo
  releaseShow.value = true
}

const showIndex = () => {
  showList()
  activeTagName.value = ''
  activeTagId.value = ''
  activeCategory.value = ''
  activeCategoryId.value = ''
  activeLogo.value = ''
  releaseShow.value = true
}

const hide = () => {
  releaseShow.value = false
}


defineExpose({show, showIndex, hide})

</script>

<style scoped lang="scss">
.release-popup {
  margin: 15px 12px 20px;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .tips {
    color: #666666;
    font-size: 13px;
    font-weight: 400;
  }

  .classify {
    padding: 20px 0 12px;
    color: #3D3D3D;
    font-size: 16px;
  }

  .tag-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    column-gap: 10px;
    row-gap: 10px;

    .tag-item {
      position: relative;
      width: calc((100% - 30px) / 4);
      color: #3D3D3D;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      padding: 12px 0;
      border-radius: 5px;
      background-color: #F5F5F5;
    }

    .active-item {
      color: #478E87;
      background-color: #DFF7F6;
    }

    .active-item::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 200%;
      height: 200%;
      border-radius: 10px;
      border: 1px solid #93C3BF;
      transform: scale(0.5);
      transform-origin: 0 0;
      box-sizing: border-box;
    }
  }

  .check-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    border-radius: 5px 0 5px 0;
    background-color: #93C3BF;

    .check-img {
      width: 6px;
      height: 6px;
      padding: 3px;
    }
  }

  .footer-box {
    margin: 30px 0 30px;
    display: flex;
    justify-content: space-between;
    font-size: 16px;

    .left-btn {
      padding: 11px 52px;
      color: #478E87;
      border-radius: 22px;
      background-color: #DFF7F6;
    }

    .right-btn {
      padding: 11px 60px;
      color: #FFFFFF;
      border-radius: 22px;
      background-color: #478E87;
    }
  }
}
</style>