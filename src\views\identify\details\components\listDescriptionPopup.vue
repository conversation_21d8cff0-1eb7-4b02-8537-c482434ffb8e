<template>
  <van-popup
      v-model:show="listShow"
      round
      position="bottom"
  >
    <div class="list-popup">
      <div class="title-box">
        <div></div>
        <div>榜单说明</div>
        <div></div>
      </div>
      <div class="content-box pt-20">
        <div v-for="(item, index) in list" :key="index">
          <div class="content-text">{{item.title}}</div>
        </div>
      </div>
      <div class="footer-btn" @click="changeKnow">
        我知道了
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref } from "vue";



const listShow = ref(false)
const emit = defineEmits(['close'])

const list = ref([
  {title: '1、首鉴榜：近期新发求鉴首个表态数的排行，近期异鉴率高于0.5%的将不参与本排行'},
  {title: '2、热心榜：近期表态数较多的排行'},
  {title: '3、异鉴率：等于表态少数方占比，红色标识带数值%'},
  {title: '4、球友昵称下方展示当前鉴定者等级与近期异鉴率，近期未表态不展示异鉴率标识，近期异鉴率等于0是绿色标识，若近期有触发异鉴率是红色标识带数值%展示'},
])



const changeKnow = () =>{
  hide()
  emit('close')
}


const show = () => {
  listShow.value = true
}
const hide = () => {
  listShow.value = false
}


defineExpose({show, hide})

</script>

<style lang="scss" scoped>
.list-popup {
  margin: 15px 12px 20px;
  box-sizing: border-box;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .tips {
    color: #666666;
    font-size: 13px;
    font-weight: 400;
  }

  .content-box {
    font-size: 14px;
    color: #3D3D3D;
    .content-text {
      line-height: 1.4;
      margin-bottom: 20px;
    }
  }
  .footer-btn {
    color: #FFFFFF;
    font-size: 16px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #478E87;
  }
}
</style>