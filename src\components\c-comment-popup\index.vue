<template>
  <div class="pb-5">
    <van-popup :show="commentPopup" round position="bottom" @click-overlay="hide(1)">
      <div class="comment-container">
        <div v-if="replyId" class="reply-title">{{ `回复@ ${replyName}` }}</div>
        <div style="overflow-x: auto">
          <div class="image-container" v-if="imageArr.length">
            <div v-for="(item, index) in imageArr" :key="index">
              <div class="content-box">
                <img v-if="item.id" class="image-icon"
                     :src="`${item.imagePath}?imageView2/1/w/200/h/200/q/75/format/webp`"/>
                <img v-if="!item.id" class="image-icon" :src="`${item.imagePath}`"/>
                <div v-if="item.videoUrl" class="video-image">
                  <img class="video-icon" :src="videoPlayIcon"/>
                </div>
                <img class="rank-icon" :src="closeIcon" @click="handleDelete(item,index)"/>
                <div v-if="!item.censor" class="censor-box">违规</div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="opinionType && userMes?.train" class="train-box">
          <div class="train-left">
            <img class="jd-icon" :src="shieldIcon"/>
            <div class="jd-text">训练中</div>
          </div>
          <div>温馨提示：训练中的表态不参与正式统计</div>
        </div>
        <div v-if="opinionType" class="btn-box">
          <div class="btn" :class="{'yx-btn': opinionType === 1 }" @click="handleOpinion(1)">
            <img class="btn" :class="{'yx-btn': opinionType === 1 }" :src="opinionType === 1 ? yxGreenBtn : greenBtn"/>
            <div class="btn-text" :class="{'left-btn-text': opinionType === 1 }">
              {{
                opinionType === 1 && !modifyType ? '已选看正' : opinionType === 1 && modifyType ? '改为看正' :
                    opinionType !== 1 && modifyType ? '原有看正' : '看正'
              }}
            </div>
          </div>
          <div class="btn" :class="{'yx-btn': opinionType === 2 }" @click="handleOpinion(2)">
            <img class="btn" :class="{'yx-btn': opinionType === 2 }" :src="opinionType === 2 ? yxRedBtn : redBtn"/>
            <div class="btn-text" :class="{'right-btn-text': opinionType === 2 }">
              {{
                opinionType === 2 && !modifyType ? '已选看假' : opinionType === 2 && modifyType ? '改为看假' :
                    opinionType !== 2 && modifyType ? '原有看假' : '看假'
              }}
            </div>
          </div>
        </div>
        <div v-if="opinionType" class="tag-box">
          <div v-for="(item, index) in tagType" :key="index" @click="handleTag(item)">
            <div class="tag-text" :class="{'tag-text-active': tagName === item.name }">{{ item.name }}</div>
          </div>
        </div>
        <div
            v-if="info.user?.uid === userInfo.id || (!userMes?.train && info.user?.uid !== userInfo.id) || (userMes?.train && info.appraisal !== null)"
            class="content-container">
          <div class="content-box">
            <SvInputAt class="editor-box" ref="svInputAtRef" :maxHeight="'160px'" :atNumber="12"
                       :minHeight="expandType ? '160px' : '40px'" @at="handleAt" @atExceed="handleAtExceed"
                       :key="inputKey"
                       v-model:html="htmlContent" placeholder="" @click="handleClick" editable="plaintext-only"/>
          </div>
          <div class="submit-btn-container">
            <div @click="handleExpand">
              <img v-if="expandType" class="btn-icon" :src="sqIcon"/>
              <img v-if="!expandType" class="btn-icon" :src="zkIcon"/>
            </div>
            <div class="right-btn">
              <!--              <div><img class="btn-icon mr-12" :src="videoIcon" @click="handleAddImage('video')"/></div>-->
              <div v-if="!opinionType" class="reply-icon"><img class="btn-icon mr-12" :src="fastIcon"
                                                               @click="handleQuickReply"/>
                <img v-if="authStore?.replyIcon" class="new-icon mr-12" :src="newIcon"/>
              </div>
              <div><img class="btn-icon mr-12" :src="imageIcon" @click="handleAddImage('camera')"/></div>
              <div><img class="btn-icon mr-12" :src="mentionIcon" @click="handleAddUser"/></div>
              <!--              <div><img class="btn-icon mr-12" :src="emojiType ? keyboardIcon : emojiIcon" @click="handleAddEmoji"/>-->
              <!--              </div>-->
              <div class="right-btn-box btn-text" @click.stop="handleSubmitChange">发布</div>
            </div>
          </div>
        </div>
        <div v-if="!opinionType && recentList?.length" class="quick-reply-box">
          <div class="mr-10">快捷回复</div>
          <div class="reply-list">
            <div v-for="item in recentList" :key="item.id" @click="addReply(item)">
              <!--              :class="{'reply-list-active': item.id === recentId}"-->
              <div class="reply-list-item">{{ item.name }}</div>
            </div>
          </div>
        </div>
        <div v-if="userMes?.train && info.user?.uid !== userInfo.id && info?.appraisal === null" class="sub-btn"
             @click="handleSubmitChange">
          发布表态
        </div>
        <EmojiPopup v-show="emojiType" @change="handleEmoji" @close="handleAddEmoji" @del="delEmoji"/>
      </div>
    </van-popup>
    <MentionPopup ref="mentionPopupRef" @addUser="handleUser" :brandId="info?.brand" :cateId="info?.cate"/>
    <QuickReplyPopup ref="quickReplyPopupRef" :content="htmlContent" @use-reply="handleUseReply"/>
    <ImageVideo ref="ImageVideoRef" :pop-show="false" :albumMultiple="albumMultiple" :imageArr="imageArr"
                @upload-success="handleSuccess"/>
    <CDialog
        style="z-index: 99999"
        ref="commonDialogRef"
        title="温馨提示"
        btnLeftTxt="取消"
        btnRightTxt="确定"
        @rightClick="confirmSend"
        :content="opinionType === 1 ? '表态后无法修改，您确认表态看正吗？' : '表态后无法修改，您确认表态看假吗？'"
    />
    <CDialog
        style="z-index: 99999"
        ref="editDialogRef"
        title="温馨提示"
        btnLeftTxt="取消"
        btnRightTxt="确定"
        @rightClick="confirmEditHide"
        content="已有编辑内容，退出编辑将清除已编辑内容"
    />
  </div>
  <Loading ref="subLoadingRef" class="loading-box"/>
</template>

<script setup>
import {ref, computed, nextTick} from "vue";
import MentionPopup from "@/views/identify/components/mentionPopup.vue";
import QuickReplyPopup from "@/views/identify/components/quickReplyPopup.vue";
import ImageVideo from "@/views/identify/details/components/imageVideo.vue";
import EmojiPopup from "@/views/identify/details/components/emojiPopup.vue";
import CDialog from "@/components/c-dialog.vue";
import identifyApi from "@/services/identify.js";
import greenBtn from "@/assets/images/details/botGreenBtn.png"
import redBtn from "@/assets/images/details/botRedBtn.png"
import yxGreenBtn from "@/assets/images/details/yxGreen.png"
import yxRedBtn from "@/assets/images/details/yxRed.png"
import sqIcon from "@/assets/images/details/sq.png"
import zkIcon from "@/assets/images/details/zk.png"
import imageIcon from "@/assets/images/details/imageIcon.png"
import videoIcon from "@/assets/images/details/videoIcon.png"
import fastIcon from "@/assets/images/details/fastIcon.png"
import newIcon from "@/assets/images/details/newIcon.png"
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import mentionIcon from "@/assets/images/details/mentionIcon.png"
import emojiIcon from "@/assets/images/details/emojiIcon.png"
import keyboardIcon from "@/assets/images/common/keyboardIcon.png"
import closeIcon from "@/assets/images/details/closeIcon.png"
import videoPlayIcon from "@/assets/images/details/videoPlayIcon.png"
import {useAuthStore} from "@/stores/auth.js";
import commonApi from "@/services/common"
import SvInputAt from "@/views/identify/release/components/sv-input-at.vue";
import {isIOS} from "@/utils/common.js";
import {setCache, getCache} from "@/utils/cache.js";
import {debounce} from "lodash";

const authStore = useAuthStore();


const fieldRef = ref()
const subLoadingRef = ref()
const uploadType = ref(true)
const albumMultiple = ref(false)
const commentPopup = ref(false)
const emojiType = ref(false)
const valueHtml = ref('')
const svInputAtRef = ref()
const htmlContent = ref('')
const storageContent = ref('')
const inputKey = ref()
const ImageVideoRef = ref()
const expandType = ref(false)
const opinionType = ref(0)
const recentList = ref([])
const recentId = ref('')
const modifyType = ref(false)
const mentionPopupRef = ref()
const quickReplyPopupRef = ref()
const commonDialogRef = ref()
const editDialogRef = ref()
const prevAtCount = ref(0) //@出现的次数
const imageArr = ref([])
const editForm = ref()
const editType = ref(false)
const editContent = ref('')
const editId = ref()
const replyId = ref()
const replyName = ref('')
const replyType = ref(false)
const replyItem = ref()
const editShowForm = ref()
const tagName = ref()


const userInfo = computed(() => {
  return authStore.userInfo
})

const emit = defineEmits(['reset'])

const props = defineProps({
  info: {
    type: Object,
    default: {}
  },
  userMes: {
    type: Object,
    default: {}
  },
})


const tagType = ref([
  {name: '翻新'},
  {name: '修复'},
  {name: '嫁接'},
  {name: '拼图'},
])


const handleTag = (item) => {
  tagName.value = tagName.value === item.name ? '' : item.name
}

const handleDelete = async (item, index) => {
  let res = await identifyApi.delImgFile({
    id: item.id
  })
  imageArr.value.splice(index, 1)
}

const handleExpand = () => {
  expandType.value = !expandType.value
}

const handleAddImage = (type) => {
  if (uploadType.value) {
    ImageVideoRef.value.open(type)
  } else {
    showToast('您当日已达上限，不可上传')
  }
}

const handleAddUser = () => {
  let userAt = svInputAtRef.value?.getAllAtLink()
  if (userAt.length >= 12) {
    showToast('@人数已达上限')
    return
  }
  mentionPopupRef.value.open(userAt)
}

const handleQuickReply = () => {
  authStore.replyIcon = false
  htmlContent.value = svInputAtRef.value?.getContent() || ''
  quickReplyPopupRef.value.show()
}

const handleAt = () => {
  let userAt = svInputAtRef.value?.getAllAtLink()
  if (userAt.length >= 12) {
    showToast('@人数已达上限')
    return
  }
  svInputAtRef.value?.blur()
  mentionPopupRef.value.open(userAt)
}

const handleAtExceed = () => {
  svInputAtRef.value?.blur()
  showToast('@人数已达上限')
}


const handleClick = () => {
  emojiType.value = false
}

const imgCount = async () => {
  // 判断上传文件数量是否达到每日上限
  let res = await commonApi.getGold({id: authStore.userInfo.id})
  if (res.code === 200) {
    return res.data.list
  }
}

const handleSuccess = async (files) => {
  imageArr.value.push({
    id: files.id,
    censor: files.censor,
    imagePath: files.src
  });
}


const handleAddEmoji = () => {
  emojiType.value = !emojiType.value
  if (!emojiType.value) {
    svInputAtRef.value?.focus()
  }
}

const delEmoji = () => {
  svInputAtRef.value?.backspace()
}


const addReply = (item) => {
  quickReplyPopupRef.value.handleUse(item)
}

const handleUseReply = (val) => {
  let str = svInputAtRef.value?.getContent() || ''
  if (!filterSpaces(str) || str === '<br>') {
    str = ''
  }
  recentId.value = val.id
  if (isIOS()) {
    svInputAtRef.value?.iosNoKeyboardEffect(() => {
      svInputAtRef.value?.initContent(str + val.name)
    })
  } else {
    svInputAtRef.value?.noKeyboardEffect(() => {
      svInputAtRef.value?.initContent(str + val.name)
    })
  }
  htmlContent.value = str + val.name
  svInputAtRef.value?.cursorToEnd()
  recentList.value = getCache('OFTEN_REPLY_MAP') ? JSON.parse(getCache('OFTEN_REPLY_MAP')) : [];
}

const handleUser = (list) => {
  svInputAtRef.value?.insertAts(list);
  // const userAt = svInputAtRef.value?.getAllAtLink();
  // if (userAt.length) {
  //   const isUserAlreadyAt = userAt.some(item => item.id === res.id);
  //   if (!isUserAlreadyAt) {
  //     svInputAtRef.value?.insertAt(res);
  //   } else {
  //     setTimeout(() => {
  //       svInputAtRef.value?.deleteString('@')
  //     })
  //     showToast('您已经@过TA了')
  //   }
  // } else {
  //   svInputAtRef.value?.insertAt(res);
  // }
};

const handleEmoji = (item) => {
  if (isIOS()) {
    svInputAtRef.value?.iosNoKeyboardEffect(() => {
      svInputAtRef.value?.insertText(item)
    })
  } else {
    svInputAtRef.value?.noKeyboardEffect(() => {
      svInputAtRef.value?.insertText(item)
    })
  }
}

// const uploaderFile = async (list) => {
//   const resultIds = []; // 存储成功上传的图片 ID
//   // 创建一个 Promise 数组，用于并发上传文件
//   const uploadPromises = list.map(async (item) => {
//
//     if (item.id) {
//       return item.id;
//     }
//
//     if (item.file) {
//       const formData = new FormData();
//       formData.append("file", item.file);
//       try {
//         const res = await identifyApi.uploadFile(userInfo.value.id, formData);
//         if (res.code === 200) {
//           return res.data.id; // 返回图片的 ID
//         } else {
//           return showToast(res.message)
//         }
//       } catch (error) {
//         return null; // 如果上传失败，返回 null
//       }
//     } else {
//       return null; // 如果文件无效，返回 null
//     }
//   });
//
//   // 等待所有上传任务完成
//   const uploadResults = await Promise.all(uploadPromises);
//   // 判断上传文件数量是否达到每日上限
//   let res = await commonApi.getGold({id: authStore.userInfo.id})
//   if (res.code === 200) {
//     const num = res.data.list.todayAttach
//     const maxNum = res.data.list.attachAll
//     // maxNum=0表示无限制
//     if (maxNum > 0 && uploadResults.length + num > maxNum) {
//       showToast(`该用户组每日最多可传` + maxNum + `张图片，已达到上限`);
//       return;
//     }
//   }
//   // 过滤掉失败的上传结果（null），只保留成功的图片 ID
//   uploadResults.forEach((id) => {
//     if (id !== null) {
//       resultIds.push(id);
//     }
//   });
//   return resultIds;
// };


const getFileId = async (list) => {
  const resultIds = []
  if (list.length) {
    list.forEach((item) => {
      resultIds.push(item.id);
    })
  }
  return resultIds;
}


//判断是否超过72小时
const isExpired = (createTimeStr) => {
  const createTime = new Date(createTimeStr);
  if (isNaN(createTime.getTime())) {
    throw new Error("createTime 格式无效，应为 'YYYY-MM-DD HH:MM:SS'");
  }
  const cutoffTime = new Date(createTime.getTime() + 72 * 60 * 60 * 1000);
  const now = new Date();
  return now > cutoffTime;
}

const addAppraisal = async () => {
  subLoadingRef.value.show()
  let res = await identifyApi.addAppraisal({
    uid: userInfo.value.id,
    partition: props.info.partition,
    tid: props.info.id,
    appraisal: opinionType.value === 1,
    tag: tagName.value || ''
  })
  if (res.code === 200) {
    console.log('res.code先执行')
    if (props.info?.redTime > 0) {
      opinionType.value === 1 ? showToast('您已表态看正，待开奖') : showToast('您已表态看假，待开奖')
    } else {
      let timer = isExpired(props.info.createTimeOrigin)
      if (opinionType.value === 1 && props.info?.redTime <= 0) {
        timer ? showToast('已表态看正') : showToast('已表态看正 经验+1')
      } else if (opinionType.value === 2 && props.info?.redTime <= 0) {
        timer ? showToast('已表态看假') : showToast('已表态看假 经验+1')
      }
    }
    const currentTime = Date.now(); // 获取当前时间戳（毫秒级）
    setCache('lastSubmitTime', currentTime.toString())
    subLoadingRef.value.hide()
    return true
  } else if (res.code === 4000) {
    showToast('该帖暂不符合训练条件，请换个试试')
    return false
  } else {
    return false
  }
}


const editAppraisal = async () => {
  subLoadingRef.value.show()
  let res = await identifyApi.editAppraisal({
    tid: props.info.id,
    partition: props.info.partition,
    tag: tagName.value || ''
  })
  if (res.code === 200) {
    console.log('res.code先执行')
    opinionType.value === 1 ? showToast('已修改为表态看正') : showToast('已修改为表态看假')
    subLoadingRef.value.hide()
    return true
  } else if (res.code === 4000) {
    showToast('该帖暂不符合训练条件，请换个试试')
    return false
  } else {
    return false
  }
}

const filterSpaces = (text) => {
  return text.trim().length > 0;
}

const convertLinksToTags = (str) => {
  const anchors = [];
  let index = 0;
  const tempStr = str.replace(/<a\s[^>]*>.*?<\/a>/gi, (match) => {
    anchors.push(match);  // 保存原始标签
    return `%%%ANCHOR_${index++}%%%`; // 用唯一占位符替换
  });
  const converted = tempStr.replace(
      /\bhttps?:\/\/(?:[a-z0-9-]+\.)*badmintoncn\.com\S*/gi,
      (url) => `<a href="${url}" target="_blank">${url}</a>`
  );
  return converted.replace(/%%%ANCHOR_(\d+)%%%/g, (_, idx) => anchors[idx]);
};


//检查是否超过10秒
const isSubmitAllowed = () => {
  const storedTime = getCache("lastSubmitTime");
  if (!storedTime) return true;
  const currentTime = Date.now();
  const lastTime = parseInt(storedTime, 10);
  const timeDiff = currentTime - lastTime;

  return timeDiff > 10000; // 10秒 = 10000毫秒
}


const handleSubmitChange = debounce(async () => {
  await handleSubmit()
}, 500)


const handleSubmit = () => {
  if (opinionType.value) {
    let subTime = isSubmitAllowed()
    if (subTime) {
      commonDialogRef.value.show()
    } else {
      showToast('请仔细审阅后表态')
    }
  } else {
    confirmSend()
  }
}

const confirmSend = async () => {
  let str = svInputAtRef.value?.getContent() || ''
  let sub = str ? convertLinksToTags(str) : ''
  if (opinionType.value) {
    modifyType.value ? await editAppraisal() : await addAppraisal()
    console.log('confirmSend先执行')
    if (!filterSpaces(sub) || sub === '<br>' || props.userMes?.train) {
      hide()
      subLoadingRef.value.hide()
      setTimeout(() => {
        console.log('reset先执行')
        emit('reset')
      }, 1000)
      return
    }
  }
  let user = svInputAtRef.value?.getAllAtLink()
  let outputString = sub.replace(/\s+/g, '');
  if ((filterSpaces(sub) && sub !== '<br>' && outputString.length > 1) || (imageArr.value.length && outputString.length > 1)) {
    subLoadingRef.value.show()
    let imgList = []
    if (imageArr.value.length) {
      imgList = await getFileId(imageArr.value)
    }
    let subForm = {
      id: editId.value || '', //可选 编辑才传
      reply: replyId.value || '', //可选 回复某条评论才传
      uid: userInfo.value.id, //用户id
      tid: props.info.id, //帖子id
      attach: imgList.join(','),
      at: user.length ? user?.map(item => item.id).join(',') : '',
      content: sub,
    }
    if (editType.value) {
      let res = await identifyApi.addComment(subForm)
      if (res.code === 200) {
        showToast('修改回复成功')
        setTimeout(() => {
          emit('reset')
        }, 1000)
        hide()
      } else {
        showToast('接口返回错误')
      }
    } else {
      let subRes = await identifyApi.addComment(subForm)
      if (subRes.code === 200) {
        if (!opinionType.value) {
          showToast('发布回复成功')
        }
        setTimeout(() => {
          emit('reset')
        }, 1000)
        hide()
      } else {
        showToast('接口返回错误')
      }
    }
    subLoadingRef.value.hide()
  } else {
    if (outputString.length < 2) {
      showToast('抱歉，评论小于两个字符限制')
      return
    }
    if (opinionType.value) return
    showToast('请填写评论内容')
  }
}

// 判断是否为视频文件
const isVideo = (filePath) => {
  const videoExtensions = ['.mp4', '.webm', '.ogg']; // 支持的视频格式
  return videoExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
};

// 判断是否为图片文件
const isImage = (filePath) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']; // 支持的图片格式
  return imageExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
};

const handleOpinion = (type) => {
  if (modifyType.value) {
    if (opinionType.value !== type) {
      showToast('原有表态不可选择')
    }
    return
  }
  opinionType.value = type
  tagName.value = ''
  let originalStr = svInputAtRef.value?.getContent()
  if (!filterSpaces(originalStr) || originalStr === '<br>') {
    originalStr = opinionType.value === 1 ? '个人看正' : '个人看假'
  }
  let replacedStr = ''
  if (opinionType.value === 1) {
    replacedStr = originalStr.replace(/个人看假/g, '个人看正');
  } else {
    replacedStr = originalStr.replace(/个人看正/g, '个人看假');
  }
  svInputAtRef.value?.initContent(replacedStr)
}


const generateRandomKey = () => {
  return Math.random().toString(36).substr(2, 9); // 生成一个随机的9位字符串
}

const reset = () => {
  inputKey.value = generateRandomKey()
  opinionType.value = 0
  modifyType.value = false
  editType.value = false
  editId.value = ''
  replyId.value = ''
  imageArr.value = []
  imgCount().then((pic) => {
    if (pic.attachAll !== 0) {
      if (pic.attachAll === pic.todayAttach || pic.attachAll < pic.todayAttach) {
        uploadType.value = false
        return
      } else {
        uploadType.value = true
      }
    } else {
      uploadType.value = true
    }
  })
  albumMultiple.value = isIOS()
}

const open = (type) => {
  reset()
  opinionType.value = type
  recentList.value = getCache('OFTEN_REPLY_MAP') ? JSON.parse(getCache('OFTEN_REPLY_MAP')) : [];
  valueHtml.value = opinionType.value === 1 ? '个人看正' : opinionType.value === 2 ? '个人看假' : ''
  expandType.value = false
  commentPopup.value = true
  nextTick(() => {
    if (valueHtml.value) {
      htmlContent.value = valueHtml.value
      svInputAtRef.value?.initContent(valueHtml.value)
    } else {
      // htmlContent.value = ''
      // svInputAtRef.value?.initContent('')
      htmlContent.value = storageContent.value ? storageContent.value : ''
    }
    if (!opinionType.value) {
      svInputAtRef.value?.focus()
    }
  });
}

const edit = (item) => {
  reset()
  editType.value = true
  editContent.value = item.content
  editId.value = item.id
  replyId.value = item?.reply?.id
  commentPopup.value = true
  expandType.value = false
  if (item?.attachList?.length) {
    item.attachList.forEach((attach) => {
      if (isVideo(attach.path)) {
        imageArr.value.push({
          file: attach.path,
          id: attach.id,
          videoUrl: attach.path,
          imagePath: attach.thumbnail,
        })
      } else if (isImage(attach.path)) {
        imageArr.value.push({
          id: attach.id,
          file: attach.path,
          censor: attach.censor,
          imagePath: attach.path
        })
      }
    })
  }
  nextTick(() => {
    if (item.content) {
      htmlContent.value = item.content
    }
  });
}

const restore = (item) => {
  reset()
  replyId.value = item.id
  replyName.value = item?.user?.name
  commentPopup.value = true
  expandType.value = false
  nextTick(() => {
    // htmlContent.value = ''
    htmlContent.value = storageContent.value ? storageContent.value : ''
    svInputAtRef.value?.focus()
  });
}

const modify = () => {
  reset()
  modifyType.value = true
  opinionType.value = props.info?.appraisal ? 2 : 1
  valueHtml.value = opinionType.value === 1 ? '修改为个人看正' : opinionType.value === 2 ? '修改为个人看假' : ''
  expandType.value = false
  commentPopup.value = true
  nextTick(() => {
    if (valueHtml.value) {
      // htmlContent.value = valueHtml.value
      svInputAtRef.value?.initContent(valueHtml.value)
    } else {
      // htmlContent.value = storageContent.value ? storageContent.value : ''
      // htmlContent.value = ''
      // svInputAtRef.value?.initContent('')
    }
    if (!opinionType.value) {
      svInputAtRef.value?.focus()
    }
  });
}

const confirmEditHide = (type) => {
  if (type === 1) {
    if (imageArr.value.length) {
      imageArr.value.forEach((item, index) => {
        if (item.id) {
          handleDelete(item, index)
        }
      })
    }
  }
  emojiType.value = false
  // htmlContent.value = ''
  // svInputAtRef.value?.clearContent()
  // opinionType.value = 0
  commentPopup.value = false
}

const hide = (type = 2) => {
  if (type === 1) {
    if (!editType.value) {
      storageContent.value = htmlContent.value
      // storageContent.value = svInputAtRef.value?.getContent() || ''
      confirmEditHide()
    }
    if (editType.value && htmlContent.value !== editContent.value) {
      editDialogRef.value.show()
    } else if (editType.value) {
      confirmEditHide()
    }
  } else {
    if (!editType.value) {
      storageContent.value = ''
    }
    confirmEditHide(2)
  }
}

defineExpose({open, hide, edit, restore, modify})

</script>

<style scoped lang="scss">
.comment-container {
  //height: 200px;
  background-color: #FFFFFF;

  .reply-title {
    margin: 12px 12px 0;
    color: #999999;
    font-size: 12px;
    font-weight: 400;
  }

  .train-box {
    padding-top: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666666;
    font-size: 13px;

    .train-left {
      display: flex;
      align-items: center;
      margin-right: 8px;
      border-radius: 3px;
      background-color: #9673FF;

      .jd-icon {
        width: 9px;
        height: 10px;
        padding: 3px;
      }

      .jd-text {
        color: #FFFFFF;
        font-size: 10px;
        padding-right: 3px;
      }
    }
  }

  .image-container {
    margin: 12px 12px 0;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    white-space: nowrap;

    .content-box {
      background-color: #F5F5F5;
      border-radius: 5px;
      margin-right: 10px;
      height: 80px;
      position: relative;
      overflow: hidden;

      .image-icon {
        width: 80px;
        height: 100%;
        border-radius: 5px;
      }

      .rank-icon {
        position: absolute;
        top: 0;
        right: 0;
        width: 16px;
        height: 16px;
      }

      .censor-box {
        position: absolute;
        top: 0;
        left: 0;
        width: 30px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        background: #FBFF00;
        border: 2px solid #000000;
        font-size: 12px;
        font-weight: 800;
        color: #030303;
      }

      .video-image {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        .video-icon {
          width: 25px;
          height: 25px;
        }
      }
    }
  }

  .image-container {
    overflow: auto;
    scrollbar-width: none;
  }

  .image-container::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .btn-box {
    margin-top: 15px;
    padding: 2px 24px 5px;
    display: flex;
    align-items: baseline;

    .btn {
      width: 163px;
      height: 28px;
      position: relative;
    }

    .yx-btn {
      width: 163px;
      height: 38px;
      position: relative;
    }

    .btn-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -30%);
      color: #FFFFFF;
      font-size: 16px;
      font-weight: 600;
    }

    .left-btn-text {
      transform: translate(-50%, -40%);
      color: #00B578;
    }

    .right-btn-text {
      transform: translate(-50%, -40%);
      color: #FA5151;
    }
  }

  .tag-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .tag-text {
      font-size: 13px;
      padding: 6px 20px;
      margin: 12px 10px 0 0;
      color: #3D3D3D;
      border-radius: 20px;
      background-color: #F5F5F5;
    }

    .tag-text-active {
      color: #478E87;
      background-color: #DFF7F6;
    }
  }

  .content-container {
    margin: 12px 12px 20px;
    overflow: hidden;
    border-radius: 15px;
    background-color: #F5F5F5;

    .content-box {
      .van-cell {
        padding-bottom: 0;
        background-color: #F5F5F5;
      }
    }

    .submit-btn-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px 8px;

      .btn-icon {
        width: 20px;
        height: 20px;
      }

      .reply-icon {
        position: relative;
      }

      .new-icon {
        position: absolute;
        left: 10px;
        top: -4px;
        width: 30px;
        height: 18px;
      }

      .right-btn {
        display: flex;
        align-items: flex-end;

        .btn-text {
          margin-bottom: 2px;
          height: 28px;
          line-height: 28px;
          color: #FFFFFF;
          font-size: 14px;
          font-weight: 400;
          padding: 0 15px;
          border-radius: 15px;
          background-color: #478E87;
        }
      }
    }
  }

  .quick-reply-box {
    font-size: 13px;
    color: #999999;
    font-weight: 400;
    //margin: 12px;
    margin: 12px 12px 20px;
    display: flex;
    align-items: center;

    .reply-list {
      display: flex;
      align-items: center;

      .reply-list-item {
        color: #3D3D3D;
        margin-right: 8px;
        padding: 7px;
        border-radius: 8px;
        border: 1px solid #D8D8D8;
        max-width: 110px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        min-width: 0;
      }

      .reply-list-active {
        color: #478E87;
        font-weight: 600;
        border: 1px solid #93C3BF;
        background-color: #DFF7F6;
      }
    }
  }

  .sub-btn {
    margin: 16px 12px 24px;
    color: #FFFFFF;
    font-size: 16px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    border-radius: 44px;
    background-color: #478E87;
  }
}

::v-deep .w-e-text-container {
  font-size: 14px;
  background-color: #F5F5F5;
}

.editorInput {
  ::v-deep .editor {
    padding: 8px 8px 0;
    min-height: 30px !important;
    max-height: 160px !important;
    background-color: #F5F5F5 !important;
  }
}

.editorInput-height {
  ::v-deep .editor {
    padding: 8px 8px 0;
    min-height: 160px;
    max-height: 160px;
    background-color: #F5F5F5 !important;
  }
}
</style>