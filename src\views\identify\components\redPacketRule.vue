<template>
  <van-popup
      v-model:show="ruleShow"
      round
      position="bottom"
  >
    <div class="list-popup">
      <div class="title-box">
        <div></div>
        <div>红包规则</div>
        <div></div>
      </div>
      <div class="title-tips">
        红包奖励是给参与鉴定球友的回馈，满足鉴定条件并参与鉴定的球友，可有机会参与抽取随机红包
      </div>
      <div class="content-box pt-20">
        <div v-for="(item, index) in list" :key="index">
          <div class="content-text">{{ item.title }}</div>
        </div>
      </div>
      <div class="footer-btn" @click="changeKnow">
        我知道了
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref} from "vue";


const ruleShow = ref(false)
const emit = defineEmits(['close'])

const list = ref([
  {title: '①每月每人可享3次免费求鉴，3次需分不同日期使用，免费求鉴期间可设置奖励鉴定球友，免费额度之外的求鉴需付费及奖励球友才可继续发布，设置奖励红包可大大提升鉴定者积极性，激励更多鉴定球友参与表态，奖励金额平台将预留20%作为推广费用。'},
  {title: '②奖励分配问题，根据红包个数对应不同开奖时间，时间结束前参与鉴定者将有机会获得随机红包，若鉴定人数未达红包个数时剩余红包转化为推广；若期间无人参与时将相应金额转化为推广费用以增加更多曝光满足求鉴所需。'},
  {title: '③付费求鉴次数与计费标准，每日第一次2元、第二次4元、第三次8元、第四次16元……以此类推，付费求鉴将获得相应金额的额外推广。'},
])


const changeKnow = () => {
  hide()
  emit('close')
}


const show = () => {
  ruleShow.value = true
}
const hide = () => {
  ruleShow.value = false
}


defineExpose({show, hide})

</script>

<style lang="scss" scoped>
.list-popup {
  margin: 15px 12px 20px;
  box-sizing: border-box;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .title-tips {
    margin-top: 20px;
    color: #3D3D3D;
    line-height: 1.4;
    font-size: 15px;
  }

  .content-box {
    font-size: 14px;
    color: #3D3D3D;
    font-weight: 400;

    .content-text {
      line-height: 1.4;
      margin-bottom: 20px;
    }
  }

  .footer-btn {
    color: #FFFFFF;
    font-size: 16px;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #478E87;
  }
}
</style>