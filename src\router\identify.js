const routes = [
    {
        path: "/identify/index",
        name: "identify",
        component: () => import("./../views/identify/index.vue"),
    },
    {
        path: "/brand/index",
        name: "brand",
        component: () => import("./../views/identify/brand/index.vue"),
    },
    {
        path: "/myAppraisal/index",
        name: "myAppraisal",
        component: () => import("./../views/identify/myAppraisal/index.vue"),
    },
    {
        path: "/heAppraisal/index",
        name: "heAppraisal",
        component: () => import("./../views/identify/heAppraisal/index.vue"),
    },
    {
        path: "/dataStatistics/index",
        name: "dataStatistics",
        component: () => import("./../views/identify/dataStatistics/index.vue"),
    },
    {
        path: "/brand/improve",
        name: "improve",
        component: () => import("./../views/identify/brand/improve.vue"),
    },
    {
        path: "/identify/screen",
        name: "screen",
        component: () => import("./../views/identify/screen.vue"),
    },
    {
        path: "/release/index",
        name: "release",
        component: () => import("./../views/identify/release/index.vue"),
    },
    {
        path: "/release/instructions",
        name: "instructions",
        component: () => import("./../views/identify/release/instructions.vue"),
    },
    {
        path: "/related/index",
        name: "related",
        component: () => import("./../views/identify/related/index.vue"),
    },
    {
        path: "/details/index",
        name: "details",
        component: () => import("./../views/identify/details/index.vue"),
    },
    {
        path: "/details/statistics",
        name: "statistics",
        component: () => import("./../views/identify/details/statistics.vue"),
    },
    {
        path: "/grade/index",
        name: "grade",
        component: () => import("./../views/identify/grade/index.vue"),
    },
    {
        path: "/upgrade",
        name: "upgrade",
        component: () => import("./../views/upgrade.vue"),
    },
    {
        path: "/identify/homePlate",
        name: "homePlate",
        component: () => import("./../views/identify/homePlate.vue"),
    },
]

export default routes