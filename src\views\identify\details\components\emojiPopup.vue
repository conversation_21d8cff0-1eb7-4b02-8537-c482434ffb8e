<template>
<div class="emoji-panel-container">
  <div class="emoji-container">
    <div class="emoji-panel">
      <span class="emoji-panel-text" v-for="(item, index) in emojisArr" :key="index" @click="emojiClick(item)">
        {{item}}
      </span>
    </div>
    <div class="footer-btn">
      <div class="left-btn" @click="handleDel">
        <img class="left-icon" :src="backIcon" />
      </div>
      <div class="right-btn" @click="handleClose">
        <div class="complete-btn">完成</div>
      </div>
    </div>
  </div>
</div>
</template>

<script setup>
import {ref} from "vue";
import backIcon from "@/assets/images/common/backIcon.png"

const emojisArr = ref(['😊','😁','😀','😃','😣','😞','😩','😫','😲','😟','😦','😜','😳','😋','😥','😰','🤠','😎','😇','😉','😭','😈','😕','😏','😘','😤','😡','😅','😬','😺','😻','😽','😼','🙈','🙉','🙊','🔥','👍','👎','👌','✌️','🙏','💪','👻'],)

const emit = defineEmits(['change', 'close', 'del'])
const emojiClick = (item) => {
  emit('change', item)
}

const handleClose = () => {
  emit('close')
}

const handleDel = () => {
  emit('del')
}
</script>


<style scoped lang="scss">
.emoji-panel-container {
  position: relative;
  height: 240px;
  overflow: hidden;
  transition-property: height;
  transition-duration: 0.15s;
  /* #ifndef APP-NVUE */
  will-change: height;
  /* #endif */
}
.emoji-container {
 height: 100%;
 overflow-y: auto;
}
.emoji-panel {
  font-size: 15px;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  flex-wrap: wrap;
  padding-right: 5px;
  padding-left: 7px;
  padding-bottom: 58px;
}
.emoji-panel-text {
  font-size: 26px;
  //font-size: 22px;
  margin-left: 8px;
  margin-top: 10px;
}
.footer-btn {
  position: absolute;
  bottom: 18px;
  right: 12px;
  display: flex;
  align-items: center;
  .left-btn {
    display: flex;
    background: #FFFFFF;
    border-radius: 5px;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2);
    .left-icon {
      width: 26px;
      height: 16px;
      padding: 8px 14px;
    }
  }
  .right-btn {
    margin-left: 10px;
    color: #FFFFFF;
    font-size: 14px;
    border-radius: 5px;
    background: #478E87;
    box-shadow: 0 0 5px 0 rgba(71,142,135,0.5);
    .complete-btn {
      padding: 8px 15px;
    }
  }
}
</style>