<template>
  <van-popup
      v-model:show="screenShow"
      round
      position="bottom"
      @close="hide"
  >
    <div class="screen-popup">
      <div class="title-box">
        <div></div>
        <div>筛选</div>
        <img class="close-icon" :src="closeIcon" @click="hide"/>
      </div>
      <div class="content-container">
        <div class="classify mt-20">
          <TitleTop :title="screenTab === 2 ? '鉴定品牌' : '求鉴品牌'" :right="false"/>
        </div>
        <div class="tags-box flex-vc">
          <div class="tags-page">
            <div
                v-for="(item, index) in brandList"
                :key="index"
                class="tag-item"
                :class="{'active-item': brand === item.id}"
                @click="toggleSelection(item)"
            >
              <div>{{ item.name }}</div>
              <div v-if="brand === item.id" class="check-icon">
                <img class="check-img" :src="answerIcon"/>
              </div>
            </div>
          </div>
        </div>
        <div class="classify mt-20">
          <TitleTop :title="screenTab === 2 ? '鉴定状态' : '求鉴状态'" :right="false"/>
        </div>
        <div class="tags-box flex-vc">
          <div class="tags-page">
            <div
                v-for="(item, index) in filteredTags"
                :key="index"
                class="tag-item"
                :class="{'active-item': appraisal === item.id}"
                @click="toggleAppraisal(item)"
            >
              <div>{{ item.name }}</div>
              <div v-if="appraisal === item.id" class="check-icon">
                <img class="check-img" :src="answerIcon"/>
              </div>
            </div>
          </div>
        </div>
        <div v-if="rewardShow" class="classify mt-20">
          <TitleTop :title="screenTab === 2 ? '红包鉴定' : '红包求鉴'" :right="false"/>
        </div>
        <div v-if="rewardShow" class="tags-box flex-vc">
          <div class="tags-page">
            <div
                v-for="(item, index) in rewardList"
                :key="index"
                class="tag-item"
                :class="{'active-item': reward === item.id}"
                @click="toggleReward(item)"
            >
              <div>{{ item.name }}</div>
              <div v-if="reward === item.id" class="check-icon">
                <img class="check-img" :src="answerIcon"/>
              </div>
            </div>
          </div>
        </div>

        <div class="classify mt-20">
          <TitleTop v-if="screenTab === 2"  title="其他" :right="false"/>
        </div>
        <div v-if="screenTab === 2" class="tags-box flex-vc">
          <div class="tags-page">
            <div
                v-for="(item, index) in otherList"
                :key="index"
                class="tag-item"
                :class="{'active-item': diff === item.id}"
                @click="toggleDiff(item)"
            >
              <div>{{ item.name }}</div>
              <div v-if="diff === item.id" class="check-icon">
                <img class="check-img" :src="answerIcon"/>
              </div>
              <div v-if="diff !== item.id && item.id === 1 && diffNew" class="circle-box"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-box">
        <div class="btn left-btn" @click="hide">取消</div>
        <div class="btn right-btn" @click="handleConfirm">确定</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import {ref, computed} from "vue";
import closeIcon from '@/assets/images/common/closeIcon.png'
import answerIcon from "@/assets/images/common/answerIcon.png"
import identifyApi from "@/services/identify.js";
import TitleTop from "@/components/titleTop.vue";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();


const userInfo = computed(() => {
  return authStore.userInfo
})


const props = defineProps({
  userMes: {
    type: Object,
    default: () => {
    }
  },
  rewardShow: {
    type: Boolean,
    default: false
  },
  diffNew: {
    type: Boolean,
    default: false
  },
})

const emit = defineEmits(['confirm'])

const screenShow = ref(false)
const screenTab = ref()
const brandList = ref([
  {
    "id": -1,
    "name": "全部",
  },
  {
    "id": 1,
    "name": "尤尼克斯",
  },
  {
    "id": 22,
    "name": "李宁",
  },
  {
    "id": 2,
    "name": "威克多",
  },
  {
    "id": 0,
    "name": "其他"
  }
])
const appraisalList = ref([
  {
    "id": -1,
    "name": "全部",
  },
  {
    "id": 1,
    "name": "已看正",
  },
  {
    "id": 2,
    "name": "已看假",
  }
])
const appraisalList2 = ref([
  {
    "id": -1,
    "name": "全部",
  },
  {
    "id": 1,
    "name": "待鉴定",
  },
  {
    "id": 2,
    "name": "已鉴定",
  },
  {
    "id": 3,
    "name": "争议",
  }
])
const otherList = ref([
  {
    "id": -1,
    "name": "全部",
  },
  {
    "id": 1,
    "name": "异鉴",
  },
])
const rewardList = ref([
  {
    "id": -1,
    "name": "全部",
  },
  {
    "id": 1,
    "name": "是",
  },
  {
    "id": 2,
    "name": "否",
  },
])
const brand = ref(-1)
const appraisal = ref(-1)
const reward = ref(-1)
const diff = ref(-1)


const filteredTags = computed(() => {
  if (screenTab.value === 2) {
    return appraisalList.value;
  } else {
    if (props.userMes?.train) {
      return appraisalList2.value.slice(0, 3);
    } else {
      return appraisalList2.value;
    }
  }

})


const toggleSelection = (item) => {
  brand.value = item.id
}
const toggleAppraisal = (item) => {
  appraisal.value = item.id
}

const toggleReward = (item) => {
  reward.value = item.id
}

const toggleDiff = (item) => {
  diff.value = item.id
}

const handleConfirm = () => {
  emit('confirm', {brand: brand.value, appraisal: appraisal.value, diff: diff.value, reward: reward.value})
  hide()
}

const getCount = async () => {
  let res = await identifyApi.getThreadCount({
    uid: userInfo.value.id,
    type: 2,
    target: 'brand'
  })
  console.log('res', res)
  if (res.code === 200) {
    brandList.value = res.data.list || []
  }
}

const getEaCount = async () => {
  let res = await identifyApi.getThreadCount({
    uid: userInfo.value.id,
    type: 2,
    target: 'appraisal'
  })
  if (res.code === 200) {
    appraisalList.value = res.data.list || []
  }
}


const reset = () => {
  diff.value = -1
  appraisal.value = -1
  brand.value = -1
  reward.value = -1
}

const show = async (tab) => {
  // appraisal.value = -1
  // diff.value = -1
  screenTab.value = Number(tab)
  // await getCount()
  // await getEaCount()
  screenShow.value = true
}

const hide = () => {
  screenShow.value = false
}


defineExpose({show, hide, reset})

</script>

<style scoped lang="scss">
.screen-popup {
  margin: 15px 12px 20px;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: #3D3D3D;
    font-size: 17px;

    .close-icon {
      width: 20px;
      height: 20px;
    }
  }

  .content-container {
    .classify {
      padding-bottom: 2px !important;
    }

    .tags-page {
      flex: 1;
      //margin: 10px 12px;
      display: flex;
      flex-wrap: wrap;
      //flex-wrap: nowrap;
      justify-content: flex-start;
      column-gap: 9px;

      .tag-item {
        position: relative;
        margin-top: 10px;
        //width: calc((100% - 45px) / 4);
        width: 80px;
        color: #3D3D3D;
        font-size: 14px;
        font-weight: 400;
        text-align: center;
        padding: 12px 0;
        border-radius: 5px;
        background-color: #F5F5F5;
        .circle-box {
          position: absolute;
          top: 5px;
          right: 5px;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #FF3700;
        }
      }

      .check-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        display: flex;
        border-radius: 5px 0 5px 0;
        background-color: #93C3BF;

        .check-img {
          width: 6px;
          height: 6px;
          padding: 3px;
        }
      }

      .active-item {
        color: #478E87;
        font-weight: 500;
        background-color: #DFF7F6;
      }

      .active-item::after {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 200%;
        height: 200%;
        border-radius: 10px;
        border: 1px solid #93C3BF;
        transform: scale(0.5);
        transform-origin: 0 0;
        box-sizing: border-box;
      }


    }
  }

  .footer-box {
    margin: 30px 0 20px;
    display: flex;
    justify-content: space-between;
    font-size: 16px;

    .left-btn {
      padding: 11px 68px;
      color: #478E87;
      border-radius: 22px;
      background-color: #DFF7F6;
    }

    .right-btn {
      padding: 11px 68px;
      color: #FFFFFF;
      border-radius: 22px;
      background-color: #478E87;
    }
  }
}
</style>