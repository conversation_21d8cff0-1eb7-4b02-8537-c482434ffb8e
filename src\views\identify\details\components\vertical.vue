<template>
<div class="vertical-page" v-if="newList.length">
  <div class="big-convert-box" @click="handleConvert">
    <div class="convert-box flex-vhc">
      <img class="convert-icon" :src="convertIcon"/>
    </div>
  </div>
  <div v-for="(item, index) in newList" :key="item.id" @click="handlePreview(index)">
    <div class="img-container">
      <div class="img-content">
<!--        <img class="img-box" loading="lazy" :src="`${item.path}-bigimg.webp`" />-->
        <van-image
            class="img-box"
            fit="contain"
            lazy-load
            :src="`${item.path}-bigimg.webp`"
        />
        <img v-if="!item.censor" class="img-icon" :src="violationIcon"/>
      </div>
      <div class="convert-bot">
        <div :class="{'yellow-text': item.extra}">上传于{{item.createTime}}</div>
        <div>
          <text v-if="item.extra" class="mr-10">{{'补图'}}</text>
          <text>{{item.name}}</text>
        </div>
      </div>
    </div>
  </div>
  <div class="line-divider"></div>
</div>
</template>

<script setup>
import {ref, watch} from "vue";
import {otherList, categoryBrandMap} from '@/views/identify/lists'
import {useAuthStore} from "@/stores/auth.js";
const authStore = useAuthStore();
import convertIcon from "@/assets/images/details/leftConvertIcon.png";
import violationIcon from "@/assets/images/common/violation.png"



const newList = ref([]);


const props = defineProps({
  item: {
    type: Object,
    default: () => {
    }
  }
})

const emit = defineEmits(['change'])

const handleConvert = () => {
  emit('change')
};


const handlePreview = (index) => {
  let imgs = []
  newList.value.forEach((item) => {
    imgs.push(`${item.path}-bigimg.webp`)
  })
  if(authStore.phone ) {
    window.mag.previewImage({
      current: index,
      pics: imgs
    });
  } else {
    showImagePreview({
      images: imgs,
      startPosition: index,
    });
  }
}

// const mergeArrays = (all, partial) => {
//   const result = [];
//   all.forEach((item, index) => {
//     if (partial[index] && partial[index].path) {
//       // 如果有对应的项，则合并数据
//       result.push({
//         name: item.name,
//         icon: item.icon,
//         require: item.require,
//         id: partial[index].id,
//         path: partial[index].path,
//         createTime: partial[index].createTime
//       });
//     } else {
//       // 如果没有对应的项，则只添加原始数据
//       // result.push(item);
//     }
//   });
//   return result;
// };
const mergeArrays = (partial, all) => {
  const result = [];
  partial.forEach((item, index) => {
    if (all[index] && partial[index].path && !partial[index].extra) {
      result.push({
        name: all[index].name,
        icon: all[index].icon,
        require: item.require,
        id: partial[index].id,
        extra: partial[index].extra,
        censor: partial[index].censor,
        path: partial[index].path,
        createTime: partial[index].createTime
      });
    } else if(partial[index].path){
      result.push({
        name: '其他补充',
        icon: '',
        require: partial[index].require,
        id: partial[index].id,
        extra: partial[index].extra,
        censor: partial[index].censor,
        path: partial[index].path,
        createTime: partial[index].createTime
      });
    }
  });
  return result;
};

watch(() => props.item,
    (val) => {
      let list = categoryBrandMap[val?.cateName]?.[val?.brandName] ?? categoryBrandMap[val?.cateName] ?? otherList.value;
      // newList.value = mergeArrays(list, val.attachList);
      newList.value = mergeArrays(val.attachList, list);
    }, {
      deep: true,
      immediate: true
    })
</script>

<style scoped lang="scss">
.vertical-page {
  position: relative;
  .big-convert-box{
    z-index: 2;
    position: absolute;
    top: 0;
    right: 0px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .convert-box {
    width: 32px;
    height: 32px;
    border-radius: 23px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    background: rgba(88,88,88,0.3);
    .convert-icon {
      width: 18px;
      height: 18px;
    }
  }
  .img-container {
    position: relative;
    //min-height: 100px;
    .img-content {
      height: 100%;
      .img-box {
        object-fit: contain;
        width: 375px;
        //min-height: 50px;
        //height: 100%;
      }
      .img-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 121px;
        height: 121px;
      }
    }

    .convert-bot {
      //position: absolute;
      //bottom: 0;
      //right: 0;
      //left: 0;
      display: flex;
      justify-content: space-between;
      padding: 2px 12px 6px;
      color: #999999;
      font-size: 12px;
      font-weight: 400;
      //backdrop-filter: blur(5px);
      //-webkit-backdrop-filter: blur(5px);
      background-color: #FFFFFF;
      //background: rgba(255,255,255,0.9);
      .yellow-text {
        color: #FF8F1F;
      }
    }
  }
  .line-divider {
    height: 5px;
    margin-bottom: 10px;
    background-color: #F5F5F5;
  }
}
</style>