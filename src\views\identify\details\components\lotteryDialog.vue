<template>
  <van-overlay :show="visible" z-index="999999">
    <div class="lottery-page">
      <div class="flip-container" :class="{ 'is-flipped': top }">
        <div class="flipper">
          <div class="face front" @click="handleFlip">
            <img class="hb-bg" :src="hb1"/>
            <div class="btn1" @click.stop="visible = false">
              <span>暂不抽取</span>
            </div>
          </div>
          <div class="face back">
            <img class="hb-bg" :src="hb2"/>
            <div class="bg-title">{{ redCount > 0 ? '恭喜您' : '未中奖' }}</div>
            <div class="bg-tips">{{ redCount > 0 ? '已获得随机红包奖励' : '别灰心好运在后面' }}</div>
            <div v-if="redCount > 0" class="bg-money">
              <span style="font-size: 20px">￥</span>{{ Number(redCount).toFixed(2) }}
            </div>
            <div v-if="redCount <= 0">
              <img :src="flower" class="bg-flower"/>
            </div>
            <div class="btn2" @click="handleClose">
              <span>{{ redCount > 0 ? '开心收下' : '接好运' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup>
import {ref, watch} from "vue";
import hb1 from "@/assets/images/reward/hb1.png"
import hb2 from "@/assets/images/reward/hb2.png"
import flower from "@/assets/images/reward/flower.png"
import identifyApi from "@/services/identify.js";

const visible = ref(false)
const top = ref(false)
const prize = ref(false)
const redCount = ref(0)
const handleFlip = async () => {
  // 在这里可以调用你的抽奖接口，根据返回结果设置 prize
  // 这里为了演示，我们随机设置
  // prize.value = Math.random() > 0.5;
  let res = await identifyApi.openRed({
    id: props.info?.id,
    partition: props.info?.partition,
  })
  if (res.code === 200) {
    redCount.value = res.data.list
  }
  // 延迟一点再翻转，模拟网络请求
  // setTimeout(() => {
  //   top.value = true; // 改变 top 的值，触发翻转
  // }, 300);
  top.value = true; // 改变 top 的值，触发翻转
}

const emit = defineEmits(['redClose'])

const props = defineProps({
  info: {
    type: Object,
    default: {}
  }
})

// 点击结果页按钮的处理函数
const handleClose = () => {
  visible.value = false;
  emit('redClose')
}

const show = () => {
  visible.value = true;
}

defineExpose({show})
</script>


<style lang="scss" scoped>
.lottery-page {
  display: flex;
  //margin-top: 150px;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.flip-container {
  perspective: 1000px;
}

.flip-container, .front, .back {
  width: 251px;
  height: 278px;
}

.flipper {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.face {
  position: absolute;
  top: 0;
  left: 0;
  backface-visibility: hidden;
}

.front {
  z-index: 2;

  .hb-bg {
    width: 100%;
    height: 100%;
  }

  .btn1 {
    position: absolute;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%);
    color: #FFFDEA;
    font-size: 14px;
    font-weight: 400;
    padding: 8px 20px;
    border: 1px solid #FFFDEA;
    border-radius: 15px;
    cursor: pointer;
  }
}

.back {
  transform: rotateY(180deg);

  .hb-bg {
    width: 100%;
    height: 100%;
  }

  .bg-title {
    position: absolute;
    top: 6px;
    transform: translateX(-50%);
    left: 50%;
    color: #3D3D3D;
    font-size: 16px;
    font-weight: 600;
  }

  .bg-tips {
    position: absolute;
    top: 44px;
    left: 50%;
    transform: translateX(-50%);
    color: #3D3D3D;
    font-size: 16px;
    font-weight: 400;
    white-space: nowrap;
  }

  .bg-money {
    position: absolute;
    top: 94px;
    left: 50%;
    transform: translateX(-50%);
    color: #3D3D3D;
    font-size: 40px;
    font-weight: 600;
    white-space: nowrap;
  }

  .bg-flower {
    position: absolute;
    top: 88px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 50px;
  }

  .btn2 {
    position: absolute;
    bottom: 28px;
    left: 50%;
    transform: translateX(-50%);
    color: #FF3700;
    font-size: 14px;
    font-weight: 600;
    width: 114px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    background-color: #FFE7B6;
    border-radius: 20px;
    cursor: pointer;
  }
}

.flip-container.is-flipped .flipper {
  transform: rotateY(180deg);
}

.van-overlay {
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
