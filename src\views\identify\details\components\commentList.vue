<template>
  <div class="comment-list">
    <div v-if="list.length" class="content-container">
      <div v-if="hotFlag" class="hot-title">
        <span class="hot-text">热门评论</span>
      </div>
      <div v-for="(item, index) in list" :key="index">
        <div class="user-container">
          <div class="user-left" @click="handleUser(item)">
<!--            <img class="user-image" loading="lazy" :src="item.user?.avatar"/>-->
            <van-image
                class="user-image"
                lazy-load
                :src="item.user?.avatar"
            />
            <div class="user-mes">
              <div class="detail-mes">
                <div class="user-name">{{ item.user?.name }}</div>
<!--                <div v-if="item?.user?.uid === info?.user?.uid" class="user-tag author-tag">-->
<!--                  作者-->
<!--                </div>-->
                <div class="user-number ml-5">{{ item.createTime }}</div>
              </div>
              <div class="flex-vc mt-4">
                <!--                <div class="user-tag orange" v-if="[1, 2].includes(item?.user?.groupId)">-->
                <!--                  <div>中羽团队</div>-->
                <!--                </div>-->
                <!--                <div v-if="Number(item?.user?.uid) === 117229" class="user-tag dark-green">版主</div>-->
                <!--                <div class="detail-mes" v-if="item?.user?.uid === info?.user?.uid-->
                <!--                        && ![1, 2].includes(item?.user?.groupId) && Number(item?.user?.uid) !== 117229">-->
                <!--                  <div v-if="item?.user?.groupId" class="user-tag"-->
                <!--                       :class="[Number(item?.user?.groupId) === 1 ? 'orange' :-->
                <!--                Number(item?.user?.groupId) === 2 ? 'orange' :-->
                <!--                Number(item?.user?.groupId) === 40 ? 'golden' :-->
                <!--                Number(item?.user?.groupId) === 3 ? 'green' :-->
                <!--                Number(item?.user?.gender) === 2? 'pink' : 'purple']">-->
                <!--                    {{ userGroupList[item?.user?.groupId].groupName }}-->
                <!--                  </div>-->
                <!--                </div>-->
                <GradeTag v-if="item?.user?.uid !== info?.user?.uid"  :author="item?.user?.uid === info?.user?.uid" :uid="item?.user?.uid"
                          :groupId="item?.user?.groupId" :userLevel="item?.user?.userLevel"/>
                <div v-if="item?.user?.uid === info?.user?.uid" class="user-tag author-tag">
                  求鉴者
                </div>
                <img v-if="![1, 2].includes(item?.user?.groupId) && Number(item?.user?.uid) !== 117229 &&
                           item?.user?.uid !== info?.user?.uid && item?.user?.countRecent !== 0"
                     class="shield-icon" :src="Number(item.user?.diffRecent) === 0 ? greenShield : redShield"/>
              </div>
            </div>
          </div>
          <img v-if="hotFlag" class="hot-btn" :src="hotBtn"/>
        </div>
        <div class="content-message">
          <div
              v-if="item?.appraisal === null && (item.user.groupId >= 12 || [1,2,3].includes(item.user.groupId)) &&
              item?.user?.uid !== info?.user?.uid && !item?.appraisalTrain"
              class="no-statement">
            <span class="no-statement-text">暂无表态</span>
          </div>
          <div class="flex">
            <div v-if="item?.appraisalTrain" class="train-box mt-8">
              <img class="jd-icon" :src="purpleShieldIcon"/>
              <span class="jd-text">训练表态</span>
            </div>
          </div>
          <div v-if="item?.reply?.content" class="reply-container mt-10">
            <div style="padding: 6px 8px">
              <div class="mb-2">{{ `@${item?.reply?.user?.name}` }}</div>
              <div class="content-examine"
                   v-if="item.reply.display === -1">
                <img class="lock-icon" :src="lockIcon"/>
                <div>提示：内容正在审核中请稍后…</div>
              </div>
              <div class="content-examine"
                   v-if="item.reply.display === -2">
                <img class="lock-icon" :src="lockIcon"/>
                <div>提示：内容已被系统屏蔽…</div>
              </div>
              <div
                  v-if="item.reply.display === 0 || [1,2].includes(userInfo.groupId) || Number(userInfo.id) === 117229"
                  v-html="item?.reply?.content"></div>
            </div>
          </div>
          <div v-if="item?.appraisal !== null" class="flex-vc pt-8">
            <div class="relative">
              <div class="opinion-box" v-if="item?.appraisal !== null" :class="{'red-tag' : !item?.appraisal}">
                <img class="opinion-icon" :src="item?.appraisal ? greenShieldIcon : redShieldIcon"/>
                <span class="opinion-text" :class="{'red-text' : !item?.appraisal}">{{
                    item?.appraisal ? '看正' : '看假'
                  }}</span>
              </div>
            </div>
            <div class="tag-mes">
              <div v-if="item.appraisalTag === '嫁接'" class="tag-box ml-5">{{ item.appraisalTag }}</div>
              <div v-if="item.appraisalTag === '修复'" class="tag-box orange-tag ml-5">{{ item.appraisalTag }}</div>
              <div v-if="item.appraisalTag === '翻新'" class="tag-box green-tag ml-5">{{ item.appraisalTag }}</div>
              <div v-if="item.appraisalTag === '拼图'" class="tag-box blue-tag ml-5">{{ item.appraisalTag }}</div>
            </div>
            <div v-if="item?.appraisalRegretted" class="tag-box yellow-tag ml-5">改</div>
          </div>
          <div class="content-examine"
               v-if="item.display === -1">
            <img class="lock-icon" :src="lockIcon"/>
            <div>提示：内容正在审核中请稍后…</div>
          </div>
          <div class="content-examine"
               v-if="item.display === -2">
            <img class="lock-icon" :src="lockIcon"/>
            <div>提示：内容已被系统屏蔽…</div>
          </div>
          <SvInputAt
              v-if="(item.display !== -1 && item.display !== -2 && item.content) || [1,2].includes(userInfo.groupId) || Number(userInfo.id) === 117229 "
              v-model:html="item.content"
              :key="item.id"
              class="content content-input mt-5" ref="svInputAtRef" :editable="false" @atLink="handleAtLink"/>
          <!-- 视频展示区域 -->
          <div v-if="item.attachList && item.attachList.length && item.display !== -1 && item.display !== -2"
               class="video-container">
            <div v-for="(attach, aIndex) in item.attachList" :key="aIndex" class="video-wrapper">
              <div v-if="isVideo(attach.path)" class="video-wrapper">
                <video
                    class="robot-box-video"
                    :src="attach.path"
                    :poster="attach.cover || ''"
                    controls
                    muted
                    preload="metadata"
                >
                  <source :src="attach.path" type="video/mp4">
                  您的浏览器不支持视频播放
                </video>
              </div>
              <div v-else-if="isImage(attach.path)" class="image-wrapper"
                   @click="handlePreview(item.attachList,aIndex)">
                <img
                    :src="`${attach.path}-bigimg.webp`"
                    alt="附件图片"
                    loading="lazy"
                    class="attachment-image"
                />
              </div>
              <div v-else class="unsupported-type">
                不支持的文件类型
              </div>
            </div>
          </div>
          <!--          <mp-html ref="mpHtmlRef" :content="convertImgToVideo(item.message)"/>-->
          <div v-if="item.updateBy" class="edit-time">{{ `修改时间：${item?.updateBy}` }}</div>
          <div class="content-other">
            <div class="icon-box">
              <div>{{ `第${item.position}楼` }}</div>
            </div>
            <div v-if="item.display !== -1 && item.display !== -2" class="icon-box" @click="handleReply(item)">
              <img class="image-icon" :src="commentIcon"/>
              <text>回复</text>
            </div>
            <div v-if="item.display !== -1 && item.display !== -2" class="icon-box" @click="handleLike(item)">
              <img class="image-icon" :src="item.like ? ydzIcon : likeIcon"/>
              <text>{{ item.likes }}</text>
            </div>
            <img v-if="item.display === -1 && userInfo.id === item?.user?.uid || item.display === 0 ||
                 [1,2].includes(userInfo.groupId) || Number(userInfo.id) === 117229"
                 class="more-icon" :src="commentMore" @click="handleMore(item)"/>
          </div>
          <van-divider class="line-divider" :style="{borderColor: 'f5f5f5', margin: '0'}"/>
        </div>
      </div>
    </div>
    <div v-else>
      <Empty class="empty-wrap"
             :type="((userMes?.train && info?.appraisal === null && userInfo.id !== info?.user?.uid) || (info?.red && info?.appraisal === null && userInfo.id !== info?.user?.uid)) ? 'train' : 'noContent'"/>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from "vue";
import Empty from "@/components/empty/index.vue"
import GradeTag from "@/views/identify/grade/components/gradeTag.vue";
import hotBtn from "@/assets/images/details/hotBtn.png"
import commentIcon from "@/assets/images/details/commentIcon.png"
import likeIcon from "@/assets/images/details/likeIcon.png"
import ydzIcon from "@/assets/images/details/ydz.png"
import commentMore from "@/assets/images/details/commentMore.png"
import shieldIcon from "@/assets/images/identify/shieldIcon.png"
import greenShieldIcon from "@/assets/images/identify/greenShieldIcon.png"
import redShieldIcon from "@/assets/images/identify/redShieldIcon.png"
import greenShield from "@/assets/images/common/greenShield.png"
import redShield from "@/assets/images/common/redShield.png"
import lockIcon from "@/assets/images/common/lockIcon.png"
import purpleShieldIcon from "@/assets/images/identify/purpleShieldIcon.png"
import identifyApi from "@/services/identify.js";
import {getCache, setCache} from "@/utils/cache";
import {useAuthStore} from "@/stores/auth.js";

const authStore = useAuthStore();
import {useRouter} from "vue-router";
import {isMagApp, jumpLogin, jumpUser} from "@/utils/common.js";
import SvInputAt from "@/views/identify/release/components/sv-input-at.vue";

const router = useRouter()


// 用户信息
const userInfo = computed(() => {
  return authStore.userInfo
})

// 判断是否为视频文件
const isVideo = (filePath) => {
  const videoExtensions = ['.mp4', '.webm', '.ogg']; // 支持的视频格式
  return videoExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
};

// 判断是否为图片文件
const isImage = (filePath) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']; // 支持的图片格式
  return imageExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
};


const props = defineProps({
  hotFlag: {
    type: Boolean,
    default: false,
  },
  list: {
    type: Array,
    default: []
  },
  info: {
    type: Object,
    default: () => {
    }
  },
  userMes: {
    type: Object,
    default: () => {
    }
  }
})

const emit = defineEmits(['more', 'reply'])


const handleMore = (item) => {
  emit('more', item)
}

const handleReply = (item) => {
  emit('reply', item)
}


const handleAtLink = (item) => {
  jumpUser(item.id)
}

const handlePreview = (list, index) => {
  let imgs = []
  list.forEach((item) => {
    imgs.push(`${item.path}-bigimg.webp`)
  })
  if (authStore.phone) {
    window.mag.previewImage({
      current: index,
      pics: imgs
    });
  } else {
    showImagePreview({
      images: imgs,
      startPosition: index,
    });
  }
}


const handleUser = (item) => {
  if (props.info.user.uid === item.user.uid) {
    if (isMagApp()) {
      window.mag.newWin(`magapp://userHome?userId=${props.info.user.uid}`)
    } else {
      alert('请下载中羽在线APP使用')
    }
  } else {
    jumpUser(item.user.uid)
  }
}


const handleLike = async (item) => {
  if (!authStore.token) {
    jumpLogin()
    // jumpUser()
    return
  }
  if (item.user?.uid === userInfo.value.id) {
    showToast('抱歉，您不能为自己点赞')
    return
  }
  let res = await identifyApi.likeThread({uid: userInfo.value.id, tid: props.info.id, pid: item.id})
  if (res.code === 200) {
    item.like = !item.like
    item.like ? item.likes++ : item.likes--
    item.like ? showToast('您已点赞') : showToast('您已取消点赞')
  }
}

const userGroupList = computed(() => {
  return JSON.parse(getCache('USER_GROUP_LIST')).reduce((obj, item) => {
    obj[item.groupId] = item
    return obj
  }, {})
})

onMounted(() => {
  document.addEventListener('click', function (event) {
    if (event.target.tagName === 'A') {
      event.preventDefault();
      const href = event.target.getAttribute('href');
      if (isMagApp()) {
        window.mag.newWin(href);
      } else {
        window.open(href)
      }
    }
  });

})

</script>

<style lang="scss" scoped>
.comment-list {
  background-color: #FFFFFF;
  padding: 0 12px;
  position: relative;

  .content-container {
    .hot-title {
      .hot-text {
        color: #478E87;
        font-size: 10px;
        border-radius: 5px;
        padding: 5px 8px;
        background-color: #DFF7F6;
      }
    }

    .user-container {
      display: flex;
      align-items: center;
      //padding: 15px 0 10px;
      padding: 15px 0 0;
      justify-content: space-between;

      .user-left {
        display: flex;
        align-items: center;
        align-content: center;

        .user-image {
          width: 40px;
          height: 40px;
          overflow: hidden;
          border-radius: 50%;
        }

        .user-mes {
          padding-left: 8px;

          .user-name {
            color: #3D3D3D;
            font-size: 14px;
            font-weight: 600;
          }

          .detail-mes {
            display: flex;
            align-items: center;
            padding-top: 3px;

            .user-number {
              color: #999999;
              font-size: 10px;
            }
          }
        }

        .user-tag {
          display: flex;
          align-items: center;
          font-size: 10px;
          padding: 3px 4px;
          color: #FFFFFF;
          border-radius: 5px;
        }

        .author-tag {
          color: #448FFF;
          //margin-left: 5px;
          padding: 2px 3px;
          border: 1px solid #448FFF;
        }

        .pink {
          background-color: #FF7CAA;
        }

        .purple {
          background-color: #819FFF;
        }

        .orange {
          background-color: #FF8F1F;
        }

        .golden {
          color: #3D3D3D;
          font-weight: 500;
          border: 1px solid #FFE3B9;
          background: linear-gradient(315deg, #FFC689 0%, #FFE3B9 100%);
        }

        .green {
          background-color: #00AC72;
        }

        .dark-green {
          background-color: #00AC72;
        }
      }

      .hot-btn {
        width: 50px;
        height: 20px;
      }
    }

    .content-message {
      color: #3D3D3D;
      font-size: 16px;
      font-weight: 400;
      padding-left: 48px;
      word-break: break-all;

      .reply-container {
        color: #999999 !important;
        //color: #C1C1C1 !important;
        background-color: #F5F5F5;
        margin-bottom: 12px;
        font-size: 14px;
        line-height: 1.4;
        border-radius: 5px;
      }

      .edit-time {
        color: #999999;
        font-size: 12px;
        font-weight: 400;
        margin-top: 5px;
      }

      .content-other {
        display: flex;
        justify-content: space-between;
        align-content: center;
        align-items: center;
        padding: 12px 0;
        color: #A8A8A8;
        font-size: 10px;

        .icon-box {
          display: flex;
          align-items: center;

          .image-icon {
            width: 14px;
            height: 14px;
            margin-right: 5px;
          }
        }

        .more-icon {
          width: 14px;
          height: 14px;
        }
      }

      .video-container {
        margin: 5px 0;

        .video-wrapper {
          position: relative;
          margin-bottom: 8px;

          .robot-box-video {
            width: 100%;
            height: 200px;
            border-radius: 8px;
            object-fit: cover;
            background-color: #000;
          }
        }
      }

      .attachment-image {
        width: 100%;
        //height: 100%;
        border-radius: 8px;
        object-fit: cover;
      }

      .opinion-box {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        padding: 3px 5px;
        border-radius: 5px;
        background-color: #D8FAEF;

        .opinion-icon {
          width: 9px;
          height: 10px;
          margin-right: 3px;
        }

        .opinion-text {
          margin-top: 2px;
          color: #00B578;
          font-size: 10px;
        }
      }

      //.opinion-box::after {
      //  content: "";
      //  position: absolute;
      //  left: 0;
      //  top: 0;
      //  width: 200%;
      //  height: 200%;
      //  border-radius: 10px;
      //  border: 1px solid #478E87;
      //  transform: scale(0.5);
      //  transform-origin: 0 0;
      //  box-sizing: border-box;
      //}

      .tag-box {
        color: #FA5151;
        font-size: 10px;
        font-weight: 600;
        border-radius: 5px;
        padding: 4px 5px;
        background-color: #FFECEC;
      }

      .orange-tag {
        color: #FF7F00;
        background-color: #FFF1E3;
      }

      .green-tag {
        color: #00B578;
        background-color: #D7F7EC;
      }

      .blue-tag {
        color: #13A2DB;
        background-color: #DBF5FF;
      }

      .yellow-tag {
        color: #3D3D3D;
        font-weight: 700;
        padding: 3px 4px;
        background-color: #FFF64F;
      }

      .red-tag {
        background-color: #FFE7E7;
        //border: 1px solid #FA5151;
      }

      //.red-tag::after {
      //  content: "";
      //  position: absolute;
      //  left: 0;
      //  top: 0;
      //  width: 200%;
      //  height: 200%;
      //  border-radius: 5px;
      //  border: 1px solid #FA5151;
      //  transform: scale(0.5);
      //  transform-origin: 0 0;
      //  box-sizing: border-box;
      //}

      .red-text {
        color: #FA5151 !important;
      }

      .content {
        line-height: 1.5;

        ::v-deep {
          font-size: 16px !important;
          padding: 0 !important;
        }
      }

      .line {
        //height: 1rpx;
        //background-color: #F5F5F5;
      }
    }
  }
}

.shield-icon {
  width: 13px;
  height: 14px;
  margin-left: 6px;
}

::v-deep .u-divider {
  margin: -1px 0 0;

  .u-line {
    transform: unset !important;
  }
}

.empty-wrap {
  padding-top: 110px;
  height: 40vh;
}

.no-statement {
  margin-top: 8px;

  .no-statement-text {
    position: relative;
    color: #999999;
    font-size: 10px;
    font-weight: 400;
    padding: 3px 5px;
  }
}

.no-statement-text::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border-radius: 10px;
  border: 1px solid #D8D8D8;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
}

.train-box {
  display: flex;
  align-items: center;
  border-radius: 3px;
  background-color: #F1ECFF;

  .jd-icon {
    width: 11px;
    height: 12px;
    padding: 3px 5px;
  }

  .jd-text {
    color: #9673FF;
    font-size: 10px;
    font-weight: 600;
    padding-right: 5px;
  }
}

.content-examine {
  margin: 8px 0 10px;
  color: #999999;
  font-size: 12px;
  font-weight: 400;
  display: flex;
  align-items: center;
  border: 1px dashed #FA5151;

  .lock-icon {
    margin-top: -1px;
    width: 16px;
    height: 14px;
    padding: 8px 2px 8px 8px;
  }
}
</style>